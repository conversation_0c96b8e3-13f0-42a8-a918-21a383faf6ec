const axios = require('axios');
const { performance } = require('perf_hooks');
const fakeClientAdapter = require('./fakeClientAdapter');
const logger = require('../logger').child({ service: 'themeDetector' });
const AccountFonts = require('./fonts/accountFonts');
const { convertToMultipleFormats } = require('./fonts/converter');
const { uploadFonts } = require('../helpers/font');
const { model: AccountModel } = require('../resources/account/account.model');
const { slugify } = require('../util/string');
const themeDetectCache = require('./themeDetectCache');

const normalizeFontName = (fontName) => fontName.toLowerCase().replace(/\s+/g, '-');

const findFontDefinition = (allFonts, fontWithStyle) => {
  return allFonts.find((font) => {
    return (
      font.name === fontWithStyle.fontFamily &&
      font.fontStyle === fontWithStyle.fontStyle &&
      font.fontWeight === fontWithStyle.fontWeight &&
      font.src?.length > 0
    );
  });
};

const collectStyles = async (url) => {
  const { collected, fontDefinitions, fontStatistics } =
    await fakeClientAdapter.collectDomainStyles(url);

  return {
    styles: {
      fonts: collected.fonts,
      colors: collected.colors,
      radius: collected.radius,
      allCaps: collected.allCaps,
      fontWeights: collected.fontWeights,
    },
    fontStatsDetailed: fontStatistics.fontStatsDetailed,
    cookies: fontStatistics.cookies,
    definitions: {
      fonts: fontDefinitions,
    },
  };
};

const getFileExtension = (url) => {
  const path = new URL(url).pathname;
  const filename = path.split('/').pop().split('?')[0];
  return filename.includes('.') ? filename.split('.').pop() : '';
};

const filterFonts = (fonts) => {
  const systemFonts = new Set([
    'serif',
    'sans-serif',
    'monospace',
    'cursive',
    'fantasy',
    'system-ui',
    'ui-serif',
    'ui-sans-serif',
    'ui-monospace',
    'ui-rounded',
    'emoji',
    'math',
    'fangsong',
    'helvetica',
    'arial',
    'shopify-social',
    '-apple-system',
    'blinkmacsystemfont',
  ]);

  let [mainFont, secondaryFont, ...rest] = fonts
    .map(([font]) => font.split(',')[0].trim())
    .filter((font) => !systemFonts.has(font.toLowerCase()))
    .flat();

  if (!mainFont) mainFont = secondaryFont;
  if (!secondaryFont) secondaryFont = mainFont;
  try {
    const firstFont = normalizeFontName(mainFont);
    const secondFont = normalizeFontName(secondaryFont);

    return [firstFont, secondFont, ...rest];
  } catch (e) {
    return [];
  }
};

const getRawStyles = async (url) => {
  try {
    return collectStyles(url);
  } catch (e) {
    // If error is 400 and the URL doesn't already have "www.", retry with "www."
    if (e.response?.status === 400 && !url.startsWith('www.')) {
      try {
        const newUrl = `www.${url.replace(/^(https?:\/\/)?(www\.)?/, '')}`;
        logger.info({
          message: `Retry domain with www`,
          url,
          newUrl,
        });
        return collectStyles(newUrl);
      } catch (retryError) {
        logger.error({
          message: `Unable to retry with www`,
          retryError,
        });
      }
    }
    return false;
  }
};

const downloadAndAddFonts = async ({ databaseId, cookies, fontsToAdd, isWhiteLabel = false }) => {
  const results = fontsToAdd.map(async (fontDef) => {
    const name = fontDef.name;

    const hasThatCustomFont = await AccountModel.findOne({
      databaseId,
      'settings.customFonts.name': name,
    });

    if (hasThatCustomFont) return;

    let converted = {};
    try {
      const fontSourceToDownload = fontDef.src
        .map((src) => {
          return { ...src, type: getFileExtension(src.url) };
        })
        .filter((src) => src.type !== 'eot')?.[0];

      if (!fontSourceToDownload) {
        logger.warn({
          message: 'Cannot find woff/woff2/opentype font format. Skipping.',
          fontDef,
          databaseId,
        });
        return;
      }
      const origin = new URL(fontSourceToDownload.url)?.origin;
      const cookieString = cookies.map((cookie) => `${cookie.name}=${cookie.value}`).join('; ');

      const response = await axios({
        url: fontSourceToDownload.url,
        responseType: 'arraybuffer',
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36',
          Accept: '*/*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'en-US,en;q=0.9',
          Referer: origin,
          Origin: origin,
          'sec-ch-ua': '"Chromium";v="112", "Google Chrome";v="112"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'font',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          Cookie: cookieString,
        },
      });
      converted = await convertToMultipleFormats(response.data, ['woff', 'woff2']);
      await uploadFonts({
        converted,
        name,
        userId: databaseId,
        filename: name,
        isWhiteLabel,
      });

      await AccountModel.updateOne(
        { databaseId },
        {
          $push: {
            'settings.customFonts': {
              name,
              key: slugify(name),
            },
          },
        },
      );
    } catch (err) {
      logger.error({
        message: 'Error during getting/converting custom font',
        databaseId,
        fontDef,
        errorMessage: err.message,
        stack: err.stack,
      });
    }
  });

  try {
    await Promise.all(results);
  } catch (e) {
    logger.error({
      message: 'Cannot process custom font',
      databaseId,
      errorMessage: e.message,
      stack: e.stack,
    });
  }
};

const getFonts = async (databaseId, fontStatsDetailed, fonts) => {
  const { overall } = fontStatsDetailed;
  const { mostUsedFontFamily: mostUsedHeadingFontFamily } = fontStatsDetailed.headings?.statistics;
  const {
    mostUsedFontFamily: mostUsedBodyFontFamily,
    secondMostUsedFontFamily: secondMostUsedBodyFontFamily,
  } = fontStatsDetailed.bodyText?.statistics;
  const userFonts = Object.values(await AccountFonts.getFonts(databaseId));

  const processFontData = (fontFamily, fontWithStyle) => {
    if (!fontWithStyle) {
      return null;
    }

    // Try to find definition using the helper function first
    let fontDefinition = findFontDefinition(fonts, fontWithStyle);

    // Fallback to direct name matching if the helper didn't find it
    if (!fontDefinition) {
      fontDefinition = fonts.find(
        (font) => font.name === fontWithStyle.fontFamily && font.src?.length > 0,
      );

      if (!fontDefinition) return null;
    }
    fontDefinition.isAlreadyExists = userFonts.some((font) => font.family === fontFamily);

    return fontDefinition;
  };

  let mostUsedFontFamily = mostUsedHeadingFontFamily;
  let secondMostUsedFontFamily = mostUsedBodyFontFamily;

  if (!mostUsedHeadingFontFamily) {
    mostUsedFontFamily = mostUsedBodyFontFamily;
    secondMostUsedFontFamily = secondMostUsedBodyFontFamily;
  }

  const mostUsedFontWithStyle = overall.stylesSorted.find(
    (font) => font.fontFamily === mostUsedFontFamily,
  );
  const secondMostUsedFontWithStyle = overall.stylesSorted.find(
    (font) => font.fontFamily === secondMostUsedFontFamily,
  );

  const mostUsedDefinition = processFontData(mostUsedFontFamily, mostUsedFontWithStyle);
  const secondMostUsedDefinition = processFontData(
    secondMostUsedFontFamily,
    secondMostUsedFontWithStyle,
  );

  const fontDefinitions = [mostUsedDefinition, secondMostUsedDefinition].filter(Boolean);

  return {
    fontDefinitions,
    mostUsedFont: mostUsedFontFamily,
    secondMostUsedFont: secondMostUsedFontFamily,
    additionalFonts: overall.fontFamiliesSorted
      .filter((font) => ![mostUsedFontFamily, secondMostUsedFontFamily].includes(font.family))
      .map((font) => {
        return font.family;
      }),
  };
};

const getStylesAndData = async (databaseId, url, isWhiteLabel = false) => {
  const startTime = performance.now();
  const data = await getRawStyles(url);

  const dataCollectTime = performance.now();

  const { fontDefinitions, mostUsedFont, secondMostUsedFont, additionalFonts } = await getFonts(
    databaseId,
    data.fontStatsDetailed,
    data.definitions.fonts,
  );

  try {
    await themeDetectCache.storeFontDefinition(url, fontDefinitions, data.cookies);
  } catch (e) {
    logger.warn({
      message: 'Error during cache font definitions',
      databaseId,
      error: e.message,
      fontDefinitions,
      url,
    });
  }

  const getFontsTime = performance.now();
  const fontsToAdd = fontDefinitions.filter((font) => !font.isAlreadyExists);

  await downloadAndAddFonts({ databaseId, cookies: data.cookies, fontsToAdd, isWhiteLabel });
  const downloadAndAddFontsTime = performance.now();

  const filteredFonts = filterFonts(data.styles.fonts);

  let mainFont = mostUsedFont || filteredFonts[0] || filteredFonts[1];
  let secondaryFont = secondMostUsedFont || mainFont;

  mainFont = normalizeFontName(mainFont);
  secondaryFont = normalizeFontName(secondaryFont);

  data.styles.fonts = [mainFont, secondaryFont, ...additionalFonts];

  logger.info({
    message: 'Styles and data collect',
    dataCollectTime: dataCollectTime - startTime,
    getFontsTime: getFontsTime - dataCollectTime,
    downloadAndAddFontsTime: downloadAndAddFontsTime - getFontsTime,
    allTook: downloadAndAddFontsTime - startTime,
    databaseId,
    url,
    fontsToAdd,
  });

  return { ...data, mostUsedFont, secondMostUsedFont };
};

const updateFontsFor = async (databaseId, domain, autoTheme, isWhiteLabel = false) => {
  const definitionInCache = await themeDetectCache.getFontDefinition(domain);

  if (!definitionInCache) {
    const { styles } = await getStylesAndData(databaseId, domain);
    autoTheme.siteTheme = styles;
    autoTheme.save();

    return autoTheme;
  }

  await downloadAndAddFonts({
    databaseId,
    cookies: definitionInCache.cookies,
    fontsToAdd: definitionInCache.fontDefinitions,
    isWhiteLabel,
  });

  return autoTheme;
};

module.exports = {
  updateFontsFor,
  getRawStyles,
  getStylesAndData,
  getFonts,
  normalizeFontName,
  filterFonts,
};
