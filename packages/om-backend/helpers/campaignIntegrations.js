const { upsertUserAlert, removeUserAlert } = require('../resources/userAlert/helpers/mongo');
const { getAlertLock } = require('../resources/userAlert/helpers/redis');
const { getAllCampaignFields } = require('./campaigns');
const { getIntegration } = require('./integrations');
const logger = require('../logger').child({ service: 'missing-bindings-alert' });

const integrationsWithoutBinding = ['convertKit', 'cleverReach', 'mailUp'];

const getMissingFields = async (databaseId, campaignId, integrations) => {
  const bindings = [];
  const integrationTypesInCampaign = [];
  const missingBindings = [];

  for (const integration of integrations) {
    const accountIntegration = await getIntegration(databaseId, integration.id.toString());
    integrationTypesInCampaign.push(accountIntegration?.type);

    if (integrationsWithoutBinding.includes(accountIntegration?.type)) {
      continue;
    }
    bindings.push(...integration.bindings);
  }

  const integrationsWithBindings = integrationTypesInCampaign.filter(
    (type) => !integrationsWithoutBinding.includes(type),
  );

  if (!integrationsWithBindings.length) return missingBindings;

  const fields = await getAllCampaignFields(databaseId, campaignId);

  for (const field of fields) {
    if (field.customId === 'coupon_code' || field.customId === 'coupon_title') continue;

    const isBound = bindings.find(
      (binding) => binding.fieldId === field.customId && binding.externalId,
    );

    if (!isBound) missingBindings.push(field);
  }

  return missingBindings;
};

const checkMissingBindings = async (databaseId, campaign) => {
  try {
    const { integrations } = campaign.settings;

    const alert = {
      databaseId,
      type: 'MissingFieldBindings',
      links: [{ id: `${campaign._id}`, type: 'Campaign' }],
    };

    if (integrations.length) {
      const missingBindings = await getMissingFields(databaseId, campaign.id, integrations);

      const lock = await getAlertLock({ databaseId, campaignId: campaign._id, type: alert.type });

      if (!lock && missingBindings.length) {
        alert.context = { fields: missingBindings };

        return upsertUserAlert(alert);
      }
    }

    return removeUserAlert(alert);
  } catch (e) {
    logger.error({
      message: 'Error during missing binding alert creation',
      databaseId,
      campaignId: campaign.id,
      errorMessage: e.message,
    });
  }
};

module.exports = {
  checkMissingBindings,
};
