<template lang="pug">
om-modal.mini-wizard-modal.centered-modal(
  name="mini-wizard-fine-tune"
  @beforeOpen="beforeOpen"
  @beforeClose="beforeClose"
  modalClasses="mini-wizard-modal-box"
)
  template(slot="modal-body")
    .om-mini-wizard-brandkit.mb-6(v-if="isShown")
      .text-center
        .om-wizard-title.mb-3 {{ $t('onboarding.customTheme.customize.title') }}
        om-body-text(bt400md) {{ $t('onboarding.customTheme.customize.description') }}
      .row(:style="'margin-top: 1.5rem'")
        .col-9
          .wizard-theme-item
            .wizard-theme-head
              .dot(v-for="dot in 3")
            .wizard-theme-content
              TemplateFrame(
                @observable="onObservable"
                v-if="template"
                @inited="updateDimensions"
                :dimensions="boxDimensions"
                allowSsr
                :template="template"
                @contentLoaded="contentLoaded"
                :endpoint="'themekit-preview'"
                :payload="{ themeKit: payload, baseTheme: true }"
                :applyPreferredLanguage="false"
                :language="language"
                forceAddAccount
                clearOnChange
              )
        .col-3
          om-heading.text-left.mb-2(h6) {{ $t('onboarding.customTheme.customize.labels.language') }}
          LanguageChooserDropdown.mb-4(:language="language" @change="language = $event")
          template(v-if="currentLogo")
            om-heading.text-left.mb-2(h6) {{ $t('onboarding.customTheme.customize.labels.logo') }}
            #brandkit-logo.mb-4.d-flex.align-items-center
              .brandkit-logo-preview-box.d-flex.align-items-center.justify-content-center.flex-grow-1.mr-2.cursor-pointer(
                @click="showImageManager"
              )
                img(:src="currentLogo")
              om-button(
                v-if="isOriginalLogo"
                small
                iconOnly
                ghost
                icon="upload"
                @click="showImageManager"
              )
              om-button.btn-logo-delete(
                v-else
                small
                iconOnly
                ghost
                icon="trash-alt"
                @click="clearLogo"
              )
          om-heading.text-left.mb-2(h6) {{ $t('onboarding.customTheme.customize.labels.color') }}
          brand-kit-palette.mb-3(
            v-if="mainColor"
            ref="brandKitPalette"
            :color="mainColor"
            :themeColors="themeColors"
            @mainColor="mainColor = $event"
            @secondaryColors="themeColors = $event"
            @backdropVisbility="onBackdropVisibilityChange"
          )
          om-heading.text-left.mb-2(h6) {{ $t('onboarding.customTheme.customize.labels.font') }}
          brand-font.mb-4(ref="primaryFont" :value="selectedFont" @select="selectFont($event)")
          om-heading.text-left.mb-2(h6) {{ $t('onboarding.customTheme.customize.labels.secondaryFont') }}
          brand-font.mb-4(
            ref="secondaryFont"
            :value="selectedSecondaryFont"
            @select="selectSecondaryFont($event)"
          )
          om-heading.text-left.mb-2(h6) {{ $t('onboarding.customTheme.customize.labels.corners') }}
          brand-kit-radius-elements.brand-kit-radiuses(
            @select="selectRadiusType($event)"
            :value="selectedRadius ? `${selectedRadius}-type` : null"
          )
          om-button(
            secondary
            data-track="wizard-brandKit-resetTheme"
            block
            @click="init"
            :style="'margin-top: 1rem'"
          ) {{ $t('onboarding.customTheme.customize.buttons.reset') }}
      custom-font-upload-v2
      transition(name="fade")
        ImageManager(@use="onUseImage" v-if="$store.state.imageManager.showImageManager")
      .om-color-picker-backdrop(v-if="showBackdrop" @click.stop="onBackdropClick")
      .mini-wizard-bottom
        .container.h-100.d-flex.align-items-center
          .d-flex.align-items-center.h-100
            om-button(ghost @click="back")
              i.fas.fa-chevron-left.mr-2
              | {{ $t('back') }}
          om-button.next-button(primary :loading="loading" @click="goToEditor") {{ $t('onboarding.customTheme.customize.goToEditor') }}
</template>
<script>
  import CustomFontUploadV2 from '@/editor/components/modals/CustomFontUploadV2.vue';
  import TemplateFrame from '@/components/Template/TemplateFrame.vue';
  import BrandKitRadiusElements from '@/editor/components/sidebar/RadiusElements.vue';
  import BrandFont from '@/components/Wizard/BrandFont.vue';
  import BrandKitPalette from '@/components/Wizard/BrandKitPalette.vue';
  import LanguageChooserDropdown from '@/components/TemplateChooser/components/LanguageChooserDropdown.vue';
  import { mapActions, mapMutations, mapState } from 'vuex';
  import ssrParamsMixin from '@/mixins/ssrParams';
  import previewParentMixin from '@/mixins/previewParent';
  import ssrMixin from '@/mixins/ssr';
  import observableCollectionMixin from '@/mixins/observableCollection';
  import { getPaletteColors } from '@om/template-properties/src/getPaletteColors';
  import UPSERT_CUSTOM_THEME from '@/graphql/UpsertCustomTheme.gql';
  import { getNameFromURL } from '@om/template-properties/src/imageReplace';
  import { getAccountIdFromCookie } from '@/util';
  import { get as _get } from 'lodash-es';
  import UPDATE_PREFERRED_TEMPLATE_LANGUAGE from '@/graphql/UpdatePreferredTemplateLanguage.gql';

  export default {
    components: {
      TemplateFrame,
      BrandKitRadiusElements,
      BrandFont,
      BrandKitPalette,
      CustomFontUploadV2,
      LanguageChooserDropdown,
      ImageManager: () => import('@/editor/components/modals/ImageManager.vue'),
    },
    mixins: [ssrMixin, ssrParamsMixin, previewParentMixin, observableCollectionMixin],
    data() {
      return {
        loading: false,
        ssrBoxSelector: '.wizard-theme-content',
        isShown: false,
        showBackdrop: false,
        mainColor: null,
        selectedRadius: null,
        selectedFont: null,
        selectedSecondaryFont: null,
        logo: null,
        language: 'en',
      };
    },
    computed: {
      ...mapState('campaignCreation', ['template', 'autoPersonalization', 'themeKit', 'sourceUrl']),
      ...mapState('imageManager', ['images', 'allTemplateImages']),
      payload() {
        const themeKit = this.buildThemeKit();
        return {
          ...themeKit,
          image: this.usedImage,
          language: this.language,
        };
      },
      themeColors: {
        get() {
          return this.themeKit.colors.themeColors;
        },
        set(v) {
          this.setThemeColors(v);
        },
      },
      currentLogo() {
        return this.usedImage?.url;
      },
      isOriginalLogo() {
        return this.logo?.name === this.themeKit?.logo;
      },
      usedImage() {
        return (
          this.images.find(this.imageNameComparer) ??
          this.allTemplateImages.find(this.imageNameComparer)
        );
      },
    },
    created() {
      document.addEventListener('click', () => {
        this.hideColorPicker();
      });
    },
    methods: {
      ...mapMutations(['hideColorPicker', 'setPreferredTemplateLanguage']),
      ...mapMutations('campaignCreation', ['setCurrentStep', 'setThemeId', 'setThemeColors']),
      ...mapMutations('imageManager', ['showImageManager']),
      ...mapActions('campaignCreation', ['createCampaign']),
      ...mapActions('imageManager', ['fetchImages', 'fetchTemplateImages']),
      beforeOpen() {
        this.setCurrentStep('mini-wizard-fine-tune');
        this.init();
        this.isShown = true;
      },
      beforeClose() {
        this.isShown = false;
        this.intersectionObserver = null;
        window.history.replaceState(null, null, ' ');
      },
      contentLoaded() {
        this.handleResize();
        this.onContentLoaded();
      },
      onObservable(event) {
        this.intersectionRoot = document.querySelector('.om-mini-wizard-brandkit');
        this.addObservable(event.$el);
      },
      init() {
        if (!this.images?.length) {
          this.fetchImages({ themeName: this.themeKit.name });
          this.fetchTemplateImages();
        }
        this.mainColor = this.autoPersonalization.color;
        this.selectedRadius = this.autoPersonalization.rounding;
        this.selectedFont = this.autoPersonalization.mainFont;
        this.selectedSecondaryFont = this.autoPersonalization.secondaryFont;
        this.logo = this.autoPersonalization.logo?.name || this.themeKit.logo;
        this.language = this.autoPersonalization.language || 'en';
      },
      buildThemeKit() {
        const fonts = [this.selectedFont?.key, this.selectedSecondaryFont?.key || 'open-sans'];
        return {
          fonts,
          rounding: this.selectedRadius || null,
          colors: {
            mainColor: this.mainColor,
            secondaryColors: getPaletteColors(this.mainColor).slice(1),
            themeColors: this.themeColors,
          },
        };
      },
      onBackdropVisibilityChange(show) {
        this.showBackdrop = show;
      },
      onBackdropClick() {
        this.$refs.brandKitPalette.hideColorPicker();
      },
      onUseImage(image) {
        this.logo = getNameFromURL(image?.url);
      },
      async createNewTheme() {
        const themeKit = this.buildThemeKit();
        try {
          const {
            data: { result },
          } = await this.$apollo.mutate({
            mutation: UPSERT_CUSTOM_THEME,
            variables: {
              name: 'My theme',
              sourceThemeName: this.themeKit.name,
              themeKit,
              hidden: false,
              logo: this.usedImage,
            },
          });
          if (result) {
            this.setThemeId(result._id);
          }
        } catch (e) {
          console.error('Cannot save custom theme');
          this.$notify({
            type: 'error',
            message: this.$t('notifications.saveError'),
          });
        }
      },
      clearLogo() {
        this.logo = this.themeKit.logo;
      },
      imageNameComparer({ url }) {
        const pureName = getNameFromURL(url);
        const pureLogo = getNameFromURL(this.logo);

        return pureName && pureLogo && pureName === pureLogo;
      },
      selectFont(font) {
        this.selectedFont = font;
        this.$refs?.secondaryFont?.add?.(font);
      },
      selectSecondaryFont(font) {
        this.selectedSecondaryFont = font;
        this.$refs?.primaryFont?.add?.(font);
      },
      selectRadiusType(element) {
        const radius = element.type.replace('-type', '');
        this.selectedRadius = radius;
      },
      back() {
        window.history.back();
      },
      setLanguage(languageCode) {
        this.setPreferredTemplateLanguage(languageCode);
        return this.$apollo.mutate({
          mutation: UPDATE_PREFERRED_TEMPLATE_LANGUAGE,
          variables: {
            languageCode,
          },
        });
      },
      async goToEditor() {
        this.loading = true;
        await this.setLanguage(this.language);
        await this.createNewTheme();
        const r = await this.createCampaign();

        const campaignId = _get(r, 'data.createCampaign.id');
        const variantId = _get(r, 'data.createCampaign.variants.0._id');

        if (!campaignId || !variantId) {
          throw new Error('Failed to create campaign');
        }

        this.loading = false;

        window.location = `/${getAccountIdFromCookie()}/variant/${campaignId}/${variantId}/edit/new${
          this.sourceUrl
        }`;
      },
    },
  };
</script>
<style lang="sass">
  @import '@/sass/pages/_wizard.sass'
  @import "@/sass/variables/_colors.sass"

  .mini-wizard-modal
    display: flex
    justify-content: center
    overflow-y: auto
    overflow-x: hidden
    .v--modal-box.v--modal
      transform: none !important
    .brand-modal
      width: 100% !important
    .brand-modal-footer
      display: none
    .brand-modal-header
      display: none !important
    .mini-wizard-modal-box
      width: 100vw !important
      height: 100vh !important
      top: 0 !important
      left: 0 !important
      background: white

    .om-mini-wizard-brandkit
      max-width: 1200px
      margin: auto
      padding-bottom: 50px
    .wizard-theme
      &-item
        height: 100%
      &-content
        width: 100%
        .template-thumbnail-box
          border-top-left-radius: 0
          border-top-right-radius: 0
          height: 100%
    .brand-kit-radiuses
      justify-content: space-between
      .radius-elements
        flex: 0 0 calc(25% - 8px)
        margin: 0
    .wizard-radius-container
      justify-content: space-between
      margin: 0 -4px
      .radius-elements
        margin: 0 4px
    .om-color-picker-backdrop
      position: absolute
      top: 0
      left: 0
      width: 100%
      height: 100%
      z-index: 1100
    #brandkit-logo
      .btn
        height: max-content
    .brandkit-logo-preview-box
      background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMElEQVQ4T2N89uzZfwY8QFJSEp80A+OoAcMiDP7//483HTx//hx/Ohg1gIFx6IcBALl+VXknOCvFAAAAAElFTkSuQmCC")
      max-width: 15rem
      height: 3.75rem
      border: 1px solid $om-gray-300
      border-radius: 4px
      padding: .25rem
      img
        width: 100%
        height: auto
        max-height: 3.25rem
        object-fit: contain
    .om-progressive-image
      height: 100%
    .mini-wizard-bottom
      position: fixed
      bottom: 0
      left: 0
      width: 100%
      height: 3.8rem
      background-color: white
      box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1)
      color: #505763
      z-index: 10000
      .container
        max-width: 1200px
      .next-button
        margin-left: auto
        height: 3rem
        padding: 1rem 3rem
    @media screen and (max-height: 960px)
      .mini-wizard-modal .mini-wizard-modal-box
        height: auto !important
</style>
