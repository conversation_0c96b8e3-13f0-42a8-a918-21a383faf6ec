<template lang="pug">
om-modal.mini-wizard-modal.centered-modal(
  name="mini-wizard-auto-personalize"
  @beforeOpen="beforeOpen"
  @beforeClose="beforeClose"
)
  template(slot="modal-body")
    .om-wizard-auto-personalize.mt-6.pt-6(v-if="isShown")
      .lottie-loading(v-if="isGenerationLoadingForCurrentDomain")
        .lottie-container
          .monk.w-100.d-flex.justify-content-center
            img(:src="require(`@/components/Elements/Monks/svgs/analyzing.svg`)")
          .d-flex.justify-content-center.align-items-baseline.mt-6
            .analyzing {{ $t('analyzingYourDomain') }}
            .dots
              lottie(filename="three_dots")
      .pb-6(v-else)
        .text-center
          wizard-title.mb-6 {{ $t('onboarding.autoPersonalize.title') }}
          .color-previews.mt-2
            .color-preview-item(v-for="{ color, selected } in detectedColors")
              ColorChip(
                :loading="!(template && selectedColor && getContentsByBaseThemeId(baseThemeId) && themeKit)"
                :colorInHex="color"
                :selected="selected"
                @click="handleColorChange"
              )
        .row(:style="'margin: 2.25rem 0 4rem'")
          .col
            .wizard-theme-content.mx-auto
              TemplateFrameWrapper(@click="onOriginalStyle")
                template(#title)
                  span {{ $t('onboarding.autoPersonalize.template.original.title') }}
                template(#template)
                  TemplateFrame(
                    v-if="template && themeKit"
                    uniqueSelector="custom-theme"
                    @observable="onObservable"
                    @inited="updateDimensions"
                    :dimensions="boxDimensions"
                    allowSsr
                    :template="template"
                    @contentLoaded="contentLoaded"
                    clearOnChange
                    :hiddenFromTheLr="false"
                  )
                template(#footer)
                  span {{ $t('onboarding.autoPersonalize.template.original.footer') }}
              .wizard-arrow-wrapper
                WizardArrow.arrow
              TemplateFrameWrapper(@click="onAutoPersonalize")
                template(#title)
                  span {{ $t('onboarding.autoPersonalize.template.branded.title') }}
                template(#template)
                  TemplateFrame(
                    uniqueSelector="auto-detect"
                    @observable="onObservable"
                    v-if="template && selectedColor && getContentsByBaseThemeId(baseThemeId) && themeKit && !loading"
                    @inited="updateDimensions"
                    :dimensions="boxDimensions"
                    allowSsr
                    :template="template"
                    @contentLoaded="onContentLoaded"
                    clearOnChange
                    :color="selectedColor"
                    :staticContent="selectedContent"
                    :hiddenFromTheLr="false"
                    :applyPreferredLanguage="false"
                  )
                  .template-placeholder(v-else)
                    AutoPersonalizeSkeleton
                template(#footer)
                  span {{ $t('onboarding.autoPersonalize.template.branded.footer') }}
        .mini-wizard-bottom
          .container.h-100
            .d-flex.align-items-center.h-100
              om-button(ghost @click="back")
                i.fas.fa-chevron-left.mr-2
                | {{ $t('back') }}
</template>
<script>
  import runtimeConfig from '@/config/runtime';
  import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
  import { track } from '@/services/xray';
  import tinycolor from 'tinycolor2';
  import WizardTitle from '@/components/Wizard/Title.vue';
  import ColorChip from '@/components/Wizard/ColorChip.vue';
  import TemplateFrameWrapper from '@/components/MiniWizard/TemplateFrameWrapper.vue';
  import TemplateFrame from '@/components/Template/TemplateFrame.vue';
  import WizardArrow from '@/components/MiniWizard/Arrow.vue';
  import AutoPersonalizeSkeleton from '@/components/SkeletonLoader/AutoPersonalizeSkeleton/AutoPersonalizeSkeleton.vue';
  import GET_FONTS from '@/graphql/GetFonts.gql';
  import UPDATE_FONTS from '@/graphql/UpdateFonts.gql';
  import { getPaletteColors } from '@om/template-properties/src/getPaletteColors';
  import ssrParamsMixin from '@/mixins/ssrParams';
  import previewParentMixin from '@/mixins/previewParent';
  import ssrMixin from '@/mixins/ssr';
  import { getCdnUrl } from '@/config/cdn';
  import WebFontLoader from 'webfontloader';
  import observableCollectionMixin from '@/mixins/observableCollection';
  import { getNameFromURL } from '@om/template-properties/src/imageReplace';
  import { cleanDomain } from '@/util';

  const MAX_COLORS = 8;

  export default {
    components: {
      WizardTitle,
      ColorChip,
      TemplateFrameWrapper,
      TemplateFrame,
      WizardArrow,
      AutoPersonalizeSkeleton,
    },
    mixins: [ssrMixin, ssrParamsMixin, previewParentMixin, observableCollectionMixin],
    data() {
      return {
        ssrBoxSelector: '.template-frame-wrapper',
        isShown: false,
        detectedColors: [],
        availableFonts: [],
        selectedColor: null,
        url: null,
        radiusValue: '',
        loading: true,
        autoPersonalizeClickable: false,
        endpoint: 'themekit-preview',
        colors: [],
        ourFonts: [],
        fontsToLoad: [],
        mainColor: null,
        themeColors: null,
        selectedRadius: null,
        selectedFont: null,
        selectedSecondaryFont: null,
        showBackdrop: false,
        loaded: false,
        loadFontsFromAccount: null,
        image: null,
        logo: null,
        fonts: null,
        language: 'en',
      };
    },
    computed: {
      ...mapState(['preferredTemplateLanguage']),
      ...mapState('campaignCreation', [
        'siteDataByDomains',
        'template',
        'theme',
        'domain',
        'availableLanguages',
      ]),
      ...mapState('imageManager', ['images', 'allTemplateImages']),
      ...mapGetters(['databaseId']),
      ...mapGetters('autoPersonalize', ['getContentsByBaseThemeId']),
      ...mapGetters('campaignCreation', ['isGenerationLoadingForCurrentDomain']),
      siteData() {
        return this.siteDataByDomains[this.domain._id] || {};
      },
      baseThemeId() {
        return this.template._id;
      },
      selectedContent() {
        return this.getContentsByBaseThemeId(this.baseThemeId)[this.selectedColor];
      },
      themeKit() {
        return this.template.template.themeKit;
      },
      payload() {
        const themeKit = this.buildThemeKit();
        return {
          ...themeKit,
          image: this.usedImage,
          language: this.language,
        };
      },
      usedImage() {
        return (
          this.images.find(this.imageNameComparer) ??
          this.allTemplateImages.find(this.imageNameComparer)
        );
      },
    },
    watch: {
      isGenerationLoadingForCurrentDomain(v) {
        if (!v && this.isShown) {
          this.init();
        }
      },
    },
    methods: {
      ...mapActions('autoPersonalize', ['preloadContents']),
      ...mapActions('imageManager', ['fetchImages', 'fetchTemplateImages']),
      ...mapMutations('autoPersonalize', ['CLEAR_CACHED_CONTENTS']),
      ...mapMutations('campaignCreation', [
        'setCurrentStep',
        'resetAutoPersonalization',
        'setAutoPersonalizationProperty',
        'setHasEnoughData',
      ]),
      ...mapMutations('campaignCreation', { setThemeColorsInStore: 'setThemeColors' }),
      async init() {
        this.loading = true;
        this.resetAutoPersonalization();
        try {
          if (
            !this.siteData.siteTheme &&
            !this.siteData.logo &&
            (!this.siteData.language || this.siteData.language === 'en')
          )
            throw new Error('not_enough_data');

          await Promise.all([
            this.fetchImages({ themeName: this.themeKit.name }),
            this.fetchTemplateImages(),
          ]);

          let { colors = [], fonts = [], radius = [] } = this.siteData.siteTheme || {};
          this.language = this.availableLanguages.includes(this.siteData.language)
            ? this.siteData.language
            : this.preferredTemplateLanguage;

          if (!colors.length) {
            colors = [this.themeKit.colors.mainColor];
          }
          this.colors = colors;
          this.setColors(colors);
          await this.loadFonts(fonts);
          this.setFonts(fonts);
          this.setRadius(radius);
          await this.setAutoLogo();
          this.setThemeColors();
          await this.cacheContents(colors);
          this.setColors(colors, true);
          this.loading = false;
          this.$nextTick(() => {
            this.updateDimensions();
          });
        } catch (e) {
          console.log('Error during auto theme', e);
          this.setHasEnoughData(false);
          this.$nextTick(() => {
            this.onOriginalStyle();
          });
        }
      },
      beforeOpen() {
        this.setCurrentStep('mini-wizard-auto-personalize');
        this.isShown = true;
        if (!this.isGenerationLoadingForCurrentDomain) {
          this.init();
        }
      },
      beforeClose() {
        this.intersectionObserver = null;
        this.isShown = false;
        this.CLEAR_CACHED_CONTENTS();
        window.history.replaceState(null, null, ' ');
      },
      onObservable(event) {
        this.intersectionRoot = document.querySelector('.mini-wizard-modal');
        this.addObservable(event.$el);
      },
      contentLoaded() {
        this.autoPersonalizeClickable = true;
      },
      buildThemeKit() {
        const fonts = [this.selectedFont?.key, this.selectedSecondaryFont?.key || 'open-sans'];
        return {
          fonts,
          rounding: this.selectedRadius || null,
          colors: {
            mainColor: this.mainColor,
            secondaryColors: getPaletteColors(this.mainColor).slice(1),
            themeColors: this.themeColors,
          },
        };
      },

      modifiedPayload(color) {
        const basePayload = JSON.parse(JSON.stringify(this.payload));

        // keep original logo on the left template preview
        delete this.payload.image;
        basePayload.colors.mainColor = color ? tinycolor(color).toHexString() : this.selectedColor;
        basePayload.fonts = this.fontsToLoad.map(({ key }) => key);
        basePayload.rounding = this.radiusValue;
        basePayload.image = this.usedImage;
        basePayload.colors.secondaryColors = getPaletteColors(this.selectedColor).slice(1);

        return basePayload;
      },
      async setAutoLogo() {
        const { logo } = this.siteData;

        if (!logo) {
          this.logo = null;
          return;
        }
        let domain = this.domain.domain.endsWith('/')
          ? this.domain.domain.slice(0, -1)
          : this.domain.domain;

        domain = cleanDomain(domain);

        const autoLogoImage = this.images.find((image) => {
          return image.name.includes(`logo-${domain}`);
        });

        if (!autoLogoImage) {
          this.logo = null;
          return;
        }

        this.logo = getNameFromURL(autoLogoImage?.url);
      },
      onAutoPersonalize() {
        if (!this.autoPersonalizeClickable) return;

        this.setAutoPersonalizationProperty({ key: 'color', value: this.selectedColor });
        this.setAutoPersonalizationProperty({ key: 'rounding', value: this.radiusValue });
        this.setAutoPersonalizationProperty({
          key: 'mainFont',
          value: this.fontsToLoad[0] || undefined,
        });
        this.setAutoPersonalizationProperty({
          key: 'secondaryFont',
          value: this.fontsToLoad[1] || undefined,
        });

        this.setAutoPersonalizationProperty({
          key: 'logo',
          value: this.usedImage,
        });

        this.setAutoPersonalizationProperty({
          key: 'language',
          value: this.language,
        });

        track('mini-wizard-auto-personalize-accept', { themeName: this.themeKit.name });

        this.$modal.hide('mini-wizard-auto-personalize');
        this.$modal.show('mini-wizard-fine-tune');
        window.history.pushState(undefined, undefined, '#fine-tune');
      },
      async onOriginalStyle() {
        track('mini-wizard-auto-personalize-decline', { themeName: this.themeKit.name });

        const { colors, fonts, rounding } = this.themeKit;

        this.setAutoPersonalizationProperty({ key: 'color', value: colors.mainColor });
        this.setAutoPersonalizationProperty({ key: 'rounding', value: rounding });
        this.setAutoPersonalizationProperty({
          key: 'mainFont',
          value: { key: fonts[0] },
        });
        this.setAutoPersonalizationProperty({
          key: 'secondaryFont',
          value: { key: fonts[1] },
        });

        this.setAutoPersonalizationProperty({
          key: 'logo',
          value: null,
        });

        this.setAutoPersonalizationProperty({
          key: 'language',
          value: this.preferredTemplateLanguage,
        });

        this.$modal.hide('mini-wizard-auto-personalize');
        this.$modal.show('mini-wizard-fine-tune');
        window.history.pushState(undefined, undefined, '#fine-tune');
      },
      async cacheContents(colors, url = null) {
        if (!this.template) return;

        const queries = colors.map((c) => {
          return {
            method: 'post',
            url: this.customEndpointUrl(),
            data: { baseTheme: true, themeKit: this.modifiedPayload(c) },
            params: {
              v: Date.now(),
              theme: this.themeKit.id,
              color: c,
              language: this.language,
              account: this.databaseId,
            },
          };
        });

        await this.preloadContents({ queries, colors, baseThemeId: this.baseThemeId, url });
      },
      customEndpointUrl() {
        const { VUE_APP_SSR_CDN_URL: ssrUrl } = runtimeConfig;
        if (ssrUrl) {
          return `${ssrUrl}/${this.endpoint}/${this.template._id}`;
        }
        return null;
      },
      setColors(colors, setSelected) {
        if (!colors.length) return;

        const orderedColors = this.sortColorsBySaturation(colors);

        const uniqueColors = Array.from(
          new Set(orderedColors.map((c) => tinycolor(c).toHexString())),
        ).map((color, index) => ({
          color,
          selected: index === 0,
        }));

        this.detectedColors = uniqueColors.slice(0, MAX_COLORS);

        if (setSelected) {
          this.selectedColor = this.detectedColors[0].color;
        }
      },
      sortColorsBySaturation(colors) {
        return colors
          .map((color) => {
            const tc = tinycolor(color);
            const { s, v } = tc.toHsv();

            const isLight = v > 0.85;
            const isVivid = s > 0.5 && v > 0.3;

            return { color, saturation: s, isLight, isVivid };
          })
          .sort((a, b) => {
            if (a.isVivid && !b.isVivid) return -1;
            if (!a.isVivid && b.isVivid) return 1;
            if (a.isLight && !b.isLight) return 1;
            if (!a.isLight && b.isLight) return -1;
            return b.saturation - a.saturation;
          })
          .map((item) => item.color);
      },
      setFonts(fonts) {
        let [firstFont, secondFont] = fonts;

        const hasFirst = this.ourFonts.find((font) => font.key === firstFont);
        const hasSecond = this.ourFonts.find((font) => font.key === secondFont);

        if (!hasFirst) {
          firstFont = this.themeKit.fonts[0];
        }

        if (!hasSecond) {
          secondFont = this.themeKit.fonts[1];
        }

        if (hasSecond && !hasFirst) {
          firstFont = secondFont;
        }

        this.fontsToLoad = [
          this.ourFonts.find((font) => font.key === firstFont),
          this.ourFonts.find((font) => font.key === secondFont),
        ];

        this.loadTemplateFonts();
      },
      async loadTemplateFonts() {
        const googleFonts = this.fontsToLoad.filter((font) => !font.custom);
        const customFonts = this.fontsToLoad.filter((font) => font.custom);
        const customUrls = customFonts.map(
          (font) => `${getCdnUrl()}/customFonts/${this.databaseId}/${font.value}/${font.value}.css`,
        );
        const webFontConfig = {};

        if (googleFonts.length) {
          webFontConfig.google = {
            families: googleFonts.map(({ value }) => value),
          };
        }
        if (customFonts.length) {
          webFontConfig.custom = {
            families: customFonts.map(({ value }) => value),
            urls: customUrls,
          };
        }

        WebFontLoader.load(webFontConfig);
      },
      getMostCommonRadius(radius) {
        if (radius.length === 0) {
          return { mostCommonCategory: null, maxRadius: 0 };
        }

        const categories = {
          none: 0,
          small: 0,
          medium: 0,
          large: 0,
        };

        let maxRadius = 0;

        const onlyPxRadius = radius.filter(
          (item) => typeof item[0] === 'string' && item[0].endsWith('px'),
        );
        onlyPxRadius.forEach(([value, weight]) => {
          const numericValue = parseFloat(value);

          if (numericValue <= 2) {
            categories.none += weight;
          } else if (numericValue >= 3 && numericValue <= 10) {
            categories.small += weight;
          } else if (numericValue >= 11 && numericValue <= 20) {
            categories.medium += weight;
          } else {
            categories.large += weight;
          }

          if (numericValue > maxRadius) {
            maxRadius = numericValue;
          }
        });

        const mostCommonCategory = Object.keys(categories).reduce((a, b) =>
          categories[a] > categories[b] ? a : b,
        );

        return {
          mostCommonCategory,
          maxRadius,
        };
      },
      setRadius(radius) {
        if (!radius.length) {
          this.radiusValue = this.themeKit.rounding;
          return;
        }
        const { mostCommonCategory } = this.getMostCommonRadius(radius);

        this.radiusValue = mostCommonCategory || this.themeKit.rounding;
      },
      handleColorChange(e) {
        track('auto-personalize-color-change');
        this.detectedColors.forEach((colorObj) => {
          if (colorObj.color === e) {
            colorObj.selected = true;
            this.selectedColor = colorObj.color;
          } else {
            colorObj.selected = false;
          }
        });
      },
      async storeFonts(key, subsets, weights) {
        await this.$apollo.mutate({
          mutation: UPDATE_FONTS,
          variables: {
            key,
            subsets,
            weights,
          },
        });
      },
      async loadFonts(userFonts) {
        const {
          data: { fonts = {} },
        } = await this.$apollo.query({
          query: GET_FONTS,
        });
        this.ourFonts = Object.entries(fonts).map(
          ([key, { installedSubsets, family, subsets, variants, custom = false }]) => {
            return { key, value: family, subsets, weights: variants, installedSubsets, custom };
          },
        );

        const [firstFont, secondFont] = userFonts;

        this.availableFonts = Object.entries(fonts)
          .filter((font) => [firstFont, secondFont].includes(font[0]))
          .map(([key, { installedSubsets, family, subsets, variants }]) => {
            return { key, value: family, subsets, weights: variants, installedSubsets };
          });

        this.availableFonts.forEach(({ key, subsets, weights, preInstalled }) => {
          if (preInstalled) return;

          this.storeFonts(key, subsets, weights);
        });
      },
      imageNameComparer({ url }) {
        const pureName = getNameFromURL(url);
        const pureLogo = getNameFromURL(this.logo);

        return pureName && pureLogo && pureName === pureLogo;
      },
      setThemeColors() {
        const { themeColors = [] } = this.themeKit.colors;
        this.themeColors = JSON.parse(JSON.stringify(themeColors));
        this.setThemeColorsInStore(this.themeColors);
      },
      back() {
        window.history.back();
      },
    },
  };
</script>
<style lang="sass">
  .lottie-loading .dots svg
    margin-top: 2px
</style>
<style lang="sass" scoped>
  ::v-deep .v--modal-box
    width: 100vw !important
    height: 100vh !important
    top: 0 !important
    left: 0 !important
  ::v-deep .brand-modal:has(.lottie-loading)
    display: flex
    flex-direction: column
  ::v-deep .brand-modal-body:has(.lottie-loading)
    display: flex
    flex: 1
    flex-direction: column
    justify-content: center
  ::v-deep .brand-modal-header
    display: none !important
  .lottie-loading
    display: flex
    justify-content: center
    .lottie-container
      position: relative
      width: 1200px
      .monk
        img
          max-width: unset !important
      .analyzing
        font-weight: 700
        font-size: 32px
        color: #23262A
      .dots
        width: 40px
        height: 40px
        left: 725px
        top: 352px
        & > div:first-of-type
          width: 40px !important
          height: 40px !important
  .color-previews
    display: flex
    gap: 12px
    justify-content: center
    margin-bottom: 36px
  .wizard-theme-content
    display: flex
    gap: 32px
    width: fit-content
    overflow: unset
    .template-placeholder
      aspect-ratio: 592/333
      width: 100%
      height: auto
    .template-thumbnail-box-wrapper
      .template-thumbnail-iframe
        transform: scale(0.4625)
      ::v-deep .template-thumbnail-box
        border-bottom-left-radius: 0
        border-bottom-right-radius: 0
    .wizard-arrow-wrapper
      z-index: 9999
      position: absolute
      top: -18px
      left: 16px
      right: 0
      height: 60px
      display: flex
      align-items: center
      overflow: hidden
      justify-content: center
      pointer-events: none
      @media screen and (max-width: 1199px)
        top: -22px
        left: 8px
      svg
        @media screen and (max-width: 1199px)
          width: 100%
          height: 100%
  .mini-wizard-bottom
    position: fixed
    bottom: 0
    left: 0
    width: 100%
    height: 3.8rem
    background-color: white
    box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1)
    color: #505763
    z-index: 10000
</style>
