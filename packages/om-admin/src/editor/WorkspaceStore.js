/* eslint-disable no-use-before-define */

import { apolloClient } from '@/apollo';
import { objectHash } from '@/utils/objectHash';
import gql from 'graphql-tag';
import { get as _get, isEmpty as _isEmpty, set as _set, merge, setWith } from 'lodash-es';
import { nanoid } from 'nanoid';
import Vue from 'vue';
import Vuex from 'vuex';

import CLEAR_COUPONS from '@/graphql/ClearCoupons.gql';
import GET_CUSTOM_THEME_THEME_KIT from '@/graphql/GetCustomThemeThemeKit.gql';
import GET_FONTS from '@/graphql/GetFonts.gql';
import UPDATE_THEME_ELEMENT_STYLE from '@/graphql/UpdateThemeElementStyle.gql';
import GET_PPO_VARIABLE_NAMES_BY_DOMAIN from '@/graphql/GetPPOVariableNameByDomain.gql';
import GENERATE_CONTENT_WITH_AI from '@/graphql/GenerateContentWithAI.gql';

import { apiBaseUrl } from '@/config/url';
import { client, dockerClient } from '@/editor/apollo';
import { DefaultLayout as DefaultMockLayout } from '@/editor/components/MockSite.vue';
import { reinitStyles, replaceCss } from '@/editor/services/CssHelper';
import { templateInitializer } from '@/editor/services/initHelper';
import {
  addInputDefaultOptions,
  calcImageInitialWidth,
  collectElementsAllPage,
  disableLinkClicks,
  generateColId,
  generateEleId,
  generateId,
  generatePageId,
  generateRowId,
  getPageName,
  GRID_COLUMN_COUNT,
  hasPath,
  isDefined,
  isNano,
  isUrl,
  TEMPLATE_FEATURES,
} from '@/editor/util';
import { lsRetrieve, lsStore } from '@/helpers/localStorage';
import { track, trackIntercom } from '@/services/xray';
import customTheme from '@/store/customTheme';
import { urlRegex as buttonUrlRegex, isPage } from '@/util';
import { calculatePageHeight } from '@/utils/template';
import { closeGesturesDef } from '@om/editor-ssr-shared/src/core/initializers/data';
import { pickAPresentOptionsDef } from '@om/editor-ssr-shared/src/core/initializers/pickAPresentOptions';
import { scratchOptionsDef } from '@om/editor-ssr-shared/src/core/initializers/scratchCardOptions';
import { inputElements, inputTypes } from '@om/editor-ssr-shared/src/utils';
import tinyColor from 'tinycolor2';

import { MAX_ACCEPTED_RECENTLY_COLORS } from '@/config/constants';
import { getSvgsAsDataFrom } from '@/editor/util/svgLoader';
import {
  CONTENT_BY_AI,
  isFeatureEnabled,
  VISUALIZED_MARGINS,
  isEditorTipTapTextEnabled,
  isEditorTipTapButtonEnabled,
} from '@/utils/features';
import { replaceQuillContent } from '@om/custom-theme-styles/src/htmlUtil';
import defaultOverlayBackground from '@om/editor-ssr-shared/src/core/initializers/defaults/overlayBackground';
import {
  ELEMENTS,
  getStylePropertyFromType,
  QUILL_EXCEPTION,
} from '@om/template-properties/src/propertyHelper/index';
import { replaceImages, reduceElements } from '@om/template-properties/src/imageReplace';
import { logoHide } from '@om/template-properties/src/logoHide';
import { TemplateContentBuilder } from '@/services/variantBuilder/TemplateContentBuilder';
import { CommandBuilder } from '@/services/variantBuilder/CommandBuilder';
import { ElementBuilder } from '@/services/variantBuilder/ElementBuilder';
import normalizer from '@om/template-properties/src/normalizer';
import wysiwygStore from '@/store/wysiwyg';
import router from '@/router/index';
import CssGenerator from './services/CssGenerator';
import initializedServices from './services/ServiceInitializers';

const WYSIWYG_TYPES = ['OmText', 'OmButton'];

const sendTemplateBackupRequest = (dbId, caId, vId, template, method) => {
  const xhr = new XMLHttpRequest();
  xhr.onload = () => console.log('TEMPLATE BACKUP SENT');
  xhr.open('POST', `${apiBaseUrl}/${method}/template/${dbId}/${caId}`);
  xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
  xhr.send(JSON.stringify({ variantId: vId, template }));
};

const getQuillFormat = () => {
  if (window.editorRef) {
    const format = window.editorRef.getFormat();
    return format || null;
  }
};

const _vm = () => window.om.store._vm; // webpack 4 this not working workaround
const _isNano = (state) => isNano(state.template.style.mode);
const _isEmbedded = (state) => state.template.style.mode === 'embedded';
const _isSidebar = (state) => state.template.style.overlay.position !== 5;
const _clone = (v) => JSON.parse(JSON.stringify(v));
const _page = (state) => state.selectedPage;
const _boxes = (state) =>
  state.template.elements.filter(
    ({ type, pageId }) => type === 'OmRow' && pageId === _page(state).uid,
  );
const _emit = (bus, eventName, value) => {
  if (bus) bus.$emit(eventName, value);
};
const _setEditorAttr = (bus, attr, value) => {
  _emit(bus, 'setEditorStateAttr', {
    attr,
    value: _clone(value),
  });
};

const _setEditorAttrFactory = (bus) => {
  return (attr, value, defaultValue) => {
    if (!value && defaultValue) value = defaultValue;
    _setEditorAttr(bus, attr, value);
  };
};
const _setWorkspaceAttrFactory = (bus) => {
  return (attr, value, defaultValue) => {
    if (!value && defaultValue) value = defaultValue;
    _emit(bus, attr, value);
  };
};
const _findImage = (state, imageId) =>
  state.template.images.find((i) => i._id.toString() === imageId);
const _i18n = () => window.parent.om.i18n;
const _editorStore = () => window.parent.om.store;
const _apollo = (state) => (state.docker ? dockerClient : client);

const _getCustomThemeKit = async ({ themeKitName, themeKitId }) => {
  if (!themeKitName && !themeKitId) return false;

  let variables = { themeKitId };

  if (themeKitId) {
    variables = { themeKitId };
  } else {
    variables = { themeKitName };
  }

  try {
    const {
      data: { themeKit },
    } = await apolloClient.query({
      query: GET_CUSTOM_THEME_THEME_KIT,
      variables,
    });
    return _isEmpty(themeKit) ? false : themeKit;
  } catch (error) {
    console.warn(`Unable to load user custom theme: ${error}`);
  }
};

const _getPPOVariableNames = async (domain) => {
  try {
    const {
      data: {
        getPPOVariableNameByDomain: { variableNames },
      },
    } = await apolloClient.query({
      query: GET_PPO_VARIABLE_NAMES_BY_DOMAIN,
      variables: {
        domain,
      },
    });
    return variableNames;
  } catch (error) {
    console.warn(`Unable to load user PPO variables: ${error}`);
  }
};

const _saveThemeStyle = async (state, type, version, userCreated, value) => {
  const name = state.template.themeKit.name;
  const {
    data: {
      result: { success },
    },
  } = await apolloClient.mutate({
    mutation: UPDATE_THEME_ELEMENT_STYLE,
    variables: {
      theme: name,
      type,
      version,
      userCreated,
      value,
    },
  });
  _emit(_vm().$bus, 'theme-style-saved', success);
};

const _couponRedeemShopifyAvailable = (state) => {
  if (!window.parent || !window.parent.om || !window.parent.om.bus) return;
  let hasCoupon = false;
  let hasButton = false;

  for (const element of state.template.elements) {
    if (!hasCoupon && element.type === 'OmCoupon') {
      hasCoupon = true;
    }

    if (!hasButton && element.type === 'OmButton') {
      hasButton = true;
    }

    if (hasButton && hasCoupon) {
      window.parent.om.bus.$emit('couponRedeemAvailable');
      return;
    }
  }
};

const _setHasFeedbackOnPage = (state) => {
  let result = false;
  let hasFeedbackComponent = false;
  let hasRadioFeedback = false;

  state.template.elements.forEach((e) => {
    if (e.pageId === state.selectedPage.uid) {
      if (
        e.type === 'OmFeedback' ||
        e.type === 'OmSurvey' ||
        (['OmRadio', 'OmInputs'].includes(e.type) && e.data.displayType === 'button')
      )
        hasFeedbackComponent = true;
      if (['OmRadio', 'OmInputs'].includes(e.type) && e.data.feedback && e.data.feedback.question)
        hasRadioFeedback = true;
    }
  });

  state.hasRadioFeedback = hasRadioFeedback;
  _setEditorAttr(window.om.store._vm.$bus, 'hasRadioFeedback', hasRadioFeedback);

  result = hasFeedbackComponent;

  state.hasFeedbackOnPage = result;
  _setEditorAttr(window.om.store._vm.$bus, 'hasFeedbackOnPage', result);

  return result;
};

const _setCouponOnPage = (state) => {
  const hasCoupon = state.template.elements.some(
    (e) => e.pageId === state.selectedPage.uid && e.type === 'OmCoupon',
  );

  state.hasCouponOnPage = hasCoupon;
  _setEditorAttr(window.om.store._vm.$bus, 'hasCouponOnPage', hasCoupon);

  return hasCoupon;
};

const recalculateElementSize = (state, element) => {
  if (element.type === 'OmVideo' || element.type === 'OmImage') {
    const params = { element, isNano: _isNano(state) };

    if (element.type === 'OmVideo') {
      params.height = 480;
      params.width = 640;
    } else {
      const image = _findImage(state, element.desktop.background.imageId);
      params.height = image.height;
      params.width = image.width;

      const width = calcImageInitialWidth(element);

      if (!isDefined(element.desktop.smartSize.width) || width < element.desktop.smartSize.width) {
        element.desktop.smartSize.width = width;
      }
    }
  }
};
const recalculateImageSizesInRow = (state, rowId) => {
  state.template.elements.forEach((el) => {
    if (el.rowId === rowId && el.type !== 'OmCol' && el.type !== 'OmPage') {
      recalculateElementSize(state, el);
    }
  });

  _emit(_vm().$bus, 'generateCss');
};

const _setMaxPageHeight = (state) => {
  let maxHeight = 0;
  let maxUid = null;

  for (let i = 0; i < state.template.elements.length; i++) {
    const el = state.template.elements[i];

    if (el.type === 'OmPage') {
      const { uid, desktop: pageStyle } = el;
      const pageEl = document.querySelector(`#${uid}`);
      const { height } = calculatePageHeight(pageEl, pageStyle);

      if (height > maxHeight) {
        maxHeight = height;
        maxUid = uid;
      }
    }
  }

  if (maxHeight) {
    state.maxPageHeight = { value: maxHeight, pageUid: maxUid };
  }
};

const _setMaxPageHeightIfNeeded = (state) => {
  if (_isEmbedded(state)) {
    _vm().$nextTick(() => {
      _setMaxPageHeight(state);
    });
  }
};

const _handleTemplateFonts = async ({ template, state, commit, dispatch }) => {
  const { fonts: collected, realUsed } = collectFonts(template);
  commit('setUsedFonts', collected);
  commit('setRealUsedFonts', realUsed);
  try {
    await dispatch('getFonts');
    replaceCss(template, false, state.docker);
    CssGenerator.generateElementStyles(template, dispatch, state.docker);
  } catch (error) {
    console.error(`unable to replace CSS: ${error}`);
  }
};

const validationRules = [
  {
    type: 'OmButton',
    prerequisite: [{ key: 'data.action', value: 'redirect' }],
    // property: 'data.redirectUrl',
    // msg: 'invalidUrl.default',
    // url: true,
    validator: 'buttonRedirectUrl',
  },
  {
    type: 'OmImage',
    validator: 'imageRedirectUrl',
  },
  {
    type: 'OmButton',
    prerequisite: [{ key: 'data.action', value: 'dial' }],
    validator: 'buttonDial',
  },
  {
    type: 'OmInputs',
    prerequisite: [
      {
        key: 'data.required',
        value: true,
      },
    ],
    property: 'data.errorMessage',
    msg: 'fieldRequired',
  },
  {
    type: 'OmCheckbox',
    prerequisite: [
      {
        key: 'data.isPrivacyPolicy',
        value: true,
      },
    ],
    property: 'data.privacyPolicyUrl',
    msg: 'invalidUrl.default',
    url: true,
  },
  {
    type: 'OmVideo',
    prerequisite: [
      {
        key: 'data.videoType',
        value: 'youtube',
      },
    ],
    property: 'data.videoUrl',
    msg: 'invalidUrl.default',
    url: true,
  },
  {
    type: 'OmSocial',
    prerequisite: [{ key: 'data.follow', value: 'socialShare' }],
    property: 'data.socialShare.url',
    msg: 'invalidUrl.default',
    url: true,
  },
  {
    type: 'OmSocial',
    prerequisite: [{ key: 'data.follow', value: 'socialFollow' }],
    property: 'data.social',
    msg: 'invalidUrl.default',
    multiPath: true,
  },
  {
    type: 'OmCoupon',
    prerequisite: [
      { key: 'data.coupon.type', value: 'unique' },
      { key: 'data.coupon.fallbackAction', value: 'text' },
    ],
    property: 'data.coupon.fallbackCoupon',
    msg: 'fieldRequired',
  },
  {
    type: 'OmCoupon',
    prerequisite: [{ key: 'data.coupon.type', value: 'fixed' }],
    property: 'data.coupon.fixedCoupon',
    msg: 'fieldRequired',
  },
  {
    type: 'OmCoupon',
    prerequisite: [{ key: 'data.coupon.type', value: 'shopify_automatic' }],
    validator: 'shopifyAutomaticCoupon',
  },
  {
    type: 'OmCoupon',
    prerequisite: [{ key: 'data.coupon.type', value: 'followup' }],
    validator: 'followupCoupon',
  },
  {
    type: 'OmProduct',
    validator: 'product',
  },
  {
    type: 'OmCountdown',
    prerequisite: [
      {
        key: 'data.countdown.labelIsActive',
        value: true,
      },
    ],
    property: 'data.countdown.labels.days',
    msg: 'fieldRequired',
  },
  {
    type: 'OmCountdown',
    prerequisite: [
      {
        key: 'data.countdown.labelIsActive',
        value: true,
      },
    ],
    property: 'data.countdown.labels.hours',
    msg: 'fieldRequired',
  },
  {
    type: 'OmCountdown',
    prerequisite: [
      {
        key: 'data.countdown.labelIsActive',
        value: true,
      },
    ],
    property: 'data.countdown.labels.minutes',
    msg: 'fieldRequired',
  },
  {
    type: 'OmCountdown',
    prerequisite: [
      {
        key: 'data.countdown.labelIsActive',
        value: true,
      },
    ],
    property: 'data.countdown.labels.seconds',
    msg: 'fieldRequired',
  },
  {
    type: 'OmPickAPresent',
    property: 'data.presentCount',
    msg: 'fieldRequired',
  },
  {
    type: 'OmPickAPresent',
    validator: 'pickAPresentButtonValid',
  },
  {
    type: 'OmCoupon',
    validator: 'uniqueCouponValid',
  },
  {
    type: 'OmFeedback',
    validator: 'feedbackHasAction',
  },
  {
    type: 'OmSurvey',
    prerequisite: [
      {
        key: 'data.actionType',
        value: 'redirect',
      },
    ],
    validator: 'surveyButtonRedirect',
  },
  {
    type: 'OmCountdown',
    prerequisite: [
      {
        key: 'data.countdown.type',
        value: 'date',
      },
    ],
    validator: 'isCountdownValid',
  },
];

const setValidationError = (bus, value, context) => {
  // no context if variant name change failed
  if (context) {
    const { state, element } = context;

    if (state.teaserPreview) {
      state.teaserPreview = false;
    }
    _selectPage(state, element.pageId);
    if (value) _setEditorAttr(bus, 'validationError', value);
    _selectElementByUid(state, { uid: element.uid });
  } else if (value) {
    _setEditorAttr(bus, 'validationError', value);
  }
};

const _validateButtonOnPage = (state, pageId) => {
  let exists = false;
  let actionSet = false;

  state.template.elements.forEach((el) => {
    if (el.type === 'OmButton' && el.pageId === pageId) {
      exists = true;

      if (
        ['redirect', 'jumpToPage', 'nextPopup', 'dial'].includes(el.data.action) ||
        (el.data.action === 'closePopup' && el.data.status)
      ) {
        actionSet = true;
      }
    }
  });

  return { exists, actionSet };
};

const _hasButtonOnPage = (state, pageId) => {
  const { exists, actionSet } = _validateButtonOnPage(state, pageId);
  return exists && actionSet;
};

const _hasRequiredInputOnPage = (state, pageId) => {
  let result = false;

  for (const element of state.template.elements) {
    if (element.pageId === pageId && element.type === 'OmInput' && element.data.required) {
      result = true;
      break;
    }
  }

  return result;
};

const _notHasButtonOnPage = (state, pageId) => !_hasButtonOnPage(state, pageId);

const _pickAPresentButtonValid = (state, pageId) => {
  const hasRequiredInputOnPage = _hasRequiredInputOnPage(state, pageId);

  if (!hasRequiredInputOnPage) {
    return true;
  }

  const hasButtonOnPage = _hasButtonOnPage(state, pageId);

  return hasRequiredInputOnPage && hasButtonOnPage;
};

const _uniqueCouponValid = (element) => {
  if (element.data.coupon.type !== 'unique') {
    return true;
  }
  return _editorStore().state.couponCounts.available;
};

const _feedbackHasAction = (state, element) => {
  if (element.data.onClick === 'none') {
    let hasButton = false;

    for (const e of state.template.elements) {
      if (e.pageId === element.pageId && e.type === 'OmButton') {
        hasButton = true;
        break;
      }
    }

    return hasButton;
  }

  return true;
};

const validators = {
  hasButtonOnPage: (state, element) => ({
    result: _hasButtonOnPage(state, element.pageId),
    feedback: null,
  }),
  notHasButtonOnPage: (state, element) => ({
    result: _notHasButtonOnPage(state, element.pageId),
    feedback: null,
  }),
  product: (state, element) => {
    const isManual = element.data.mode === 'manual';
    const isMostViewed = element.data.mode === 'most-viewed';
    let result = true;
    let feedback = null;
    const filteredProducts = element.data.products.filter(Boolean);

    if (isManual) {
      if (!state.confirmedProductsMissing) {
        if (element.data.nrOfProducts > filteredProducts.length) {
          result = false;
          feedback = !result ? { property: 'products.count' } : null;
        }
      } else {
        element.data.products = filteredProducts;
        element.data.nrOfProducts = filteredProducts.length;
      }
    }

    if (
      !window.om.store.getters.isTemplateEditorMode &&
      isManual &&
      !state.isShopifyActive &&
      !state.isShoprenterActive
    ) {
      let property = null;
      const everyProductHasValidUrl = element.data.products.every((product, index) => {
        if (!product) return true;
        const valid = isUrl(product.url);
        if (!valid) {
          property = `selectedElement.data.products[${index}].url`;
        }
        return valid;
      });

      if (!everyProductHasValidUrl) {
        result = false;
        feedback = { property, msg: 'invalidUrl.default' };
      }
    }

    if (
      result &&
      isMostViewed &&
      element.data.productFilter &&
      element.data.productFilter.type === 'category'
    ) {
      const hasCategoryId = !!element.data.productFilter.categoryId;

      if (!hasCategoryId) {
        result = false;
        feedback = {
          property: 'selectedElement.data.productFilter.categoryId',
          msg: 'fieldRequired',
        };
      }
    }

    return {
      result,
      feedback,
    };
  },
  pickAPresentButtonValid: (state, element) => ({
    result: _pickAPresentButtonValid(state, element.pageId),
    feedback: { property: 'pickAPresentNoButton' },
  }),
  uniqueCouponValid: (_, element) => ({
    result: _uniqueCouponValid(element),
    feedback: { property: 'noCouponUploaded' },
  }),
  feedbackHasAction: (state, element) => ({
    result: _feedbackHasAction(state, element),
    feedback: { property: 'noFeedbackAction' },
  }),
  buttonRedirectUrl: (state, element) => {
    const templateEditorMode =
      state.templateSaveData.type === undefined || state.templateSaveData.type === 'base';
    let result = templateEditorMode;

    const { redirectUrl } = element.data;

    if (redirectUrl.length) {
      result =
        redirectUrl.indexOf('sms:') === 0 ||
        redirectUrl.indexOf('mailto:') === 0 ||
        buttonUrlRegex.test(redirectUrl);
    }

    return {
      result,
      feedback: { property: 'data.redirectUrl', msg: 'invalidUrl.default' },
    };
  },
  imageRedirectUrl: (state, element) => {
    let result = true;
    const { clickableImage, redirectUrl } = element.data;

    if (clickableImage) {
      result = buttonUrlRegex.test(redirectUrl);
    }

    return {
      result,
      feedback: { property: 'data.redirectUrl', msg: 'invalidUrl.invalid' },
    };
  },
  buttonDial: async (state, element) => {
    await Promise.all([import('intl-tel-input'), import('intl-tel-input/build/js/utils')]);
    const prefix = element.data.prefix.trim();
    const regex = new RegExp(`^${prefix.replace('+', '\\+')}`);
    const pnSanitized = element.data.rawPhoneNumber.trim().replace(regex, '');

    return {
      result: window.intlTelInputUtils.isValidNumber(`${prefix}${pnSanitized}`),
      feedback: { property: 'data.rawPhoneNumber', msg: 'invalidPhoneNumber' },
    };
  },
  surveyButtonRedirect: (_, element) => {
    if (element.data.sameRedirectForAllOptions && !buttonUrlRegex.test(element.data.redirectUrl)) {
      return {
        result: false,
        feedback: { property: 'surveyRedirectUrlAll', msg: 'invalidUrl.default' },
      };
    }

    const errors = [];

    let result = true;
    let index = 0;
    for (index in element.data.form.customSettings.options) {
      const option = element.data.form.customSettings.options[index];
      const isCurrentValid = buttonUrlRegex.test(option.redirectUrl);
      result = result && isCurrentValid;

      if (!isCurrentValid) errors.push({ index: parseInt(index, 10), option });
    }

    return {
      result,
      feedback: { property: 'surveyRedirectUrlOption', msg: 'invalidUrl.default', errors },
    };
  },
  followupCoupon: (state, element) => {
    const isTemplateEditorMode = window.om.store.getters.isTemplateEditorMode;

    const { limit, expiresIn } = element.data.coupon.followup;
    if (isTemplateEditorMode) {
      return {
        result: true,
      };
    }

    if (state.isShopifyActive) {
      if (limit && !expiresIn) {
        return {
          result: false,
          feedback: { property: 'data.coupon.followup.expiresIn', msg: 'fieldRequired' },
        };
      }
      return {
        result: true,
      };
    }

    if (!limit) {
      return {
        result: false,
        feedback: { property: 'data.coupon.followup.limit', msg: 'fieldRequired' },
      };
    }

    if (!expiresIn) {
      return {
        result: false,
        feedback: { property: 'data.coupon.followup.expiresIn', msg: 'fieldRequired' },
      };
    }

    return {
      result: true,
    };
  },
  shopifyAutomaticCoupon: (state, element) => {
    const isTemplateEditorMode = window.om.store.getters.isTemplateEditorMode;
    const {
      fixedValue,
      percentageValue,
      type,
      expiration,
      expiresIn,
      expirationTime,
      expirationType,
    } = element.data.coupon.automatic;

    if (!isTemplateEditorMode && !state.isShopifyActive) {
      return {
        result: false,
        feedback: { property: 'data.coupon.type', msg: 'discountPane.automatic.shopifyNotActive' },
      };
    }
    if (type === 'percentage' && !percentageValue) {
      return {
        result: false,
        feedback: { property: 'data.coupon.automatic.percentageValue', msg: 'fieldRequired' },
      };
    }
    if (type === 'fixed' && !fixedValue) {
      return {
        result: false,
        feedback: { property: 'data.coupon.automatic.fixedValue', msg: 'fieldRequired' },
      };
    }

    if (type === 'percentage' && (percentageValue <= 0 || percentageValue > 100)) {
      return {
        result: false,
        feedback: { property: 'data.coupon.automatic.percentageValue', msg: 'fieldNotPercentage' },
      };
    }

    if (type === 'fixed' && fixedValue <= 0) {
      return {
        result: false,
        feedback: { property: 'data.coupon.automatic.fixedValue', msg: 'fieldMustBePositive' },
      };
    }

    if (expiration) {
      if (expirationType === 'absolute') {
        if (new Date(expirationTime) <= new Date()) {
          return {
            result: false,
            feedback: {
              property: 'data.coupon.automatic.expirationTime',
              msg: 'fieldMustBeInFuture',
            },
          };
        }
      } else {
        if (!expiresIn) {
          return {
            result: false,
            feedback: { property: 'data.coupon.automatic.expiresIn', msg: 'fieldRequired' },
          };
        }
        if (expiresIn < 1) {
          return {
            result: false,
            feedback: { property: 'data.coupon.automatic.expiresIn', msg: 'fieldMustBePositive' },
          };
        }
      }
    }

    return {
      result: true,
    };
  },
  isCountdownValid: (state, element) => {
    const property = 'data.countdown.endDate.date';
    const date = _get(element, property);
    const endsAt = new Date(date);
    const isInvalid = !date || endsAt.getTime() <= Date.now();

    if (isInvalid) {
      return {
        result: false,
        feedback: { property, msg: 'fieldMustBeInFuture' },
      };
    }

    return { result: true };
  },
};

const validateElements = async (state, bus, pageId) => {
  // dont allow empty variant name
  if (state.updatedVariantName === '') {
    setValidationError(bus, { property: 'variantName' });
    return false;
  }

  const elementsToValidate =
    pageId === 'all'
      ? state.template.elements
      : state.template.elements.filter((ele) => ele.pageId === pageId);

  for (const element of elementsToValidate) {
    for (const rule of validationRules) {
      if (
        element.type === rule.type &&
        (!rule.prerequisite ||
          rule.prerequisite.every((prereq) => _get(element, prereq.key) === prereq.value))
      ) {
        if (rule.validator) {
          const validationData = await validators[rule.validator](state, element);
          if (!validationData.result) {
            setValidationError(bus, validationData.feedback, { state, element });
            return false;
          }
        } else if (!rule.validator) {
          const value = _get(element, rule.property);
          if (value === undefined || value.length === 0) {
            setValidationError(bus, { property: rule.property, msg: rule.msg }, { state, element });
            return false;
          }
          if (rule.url === true && !isUrl(value)) {
            const hasProtocol = /^(?:(ht|f)tp(s?):\/\/)/.test(value);
            const message =
              value.length && !hasProtocol ? 'invalidUrl.protocol' : 'invalidUrl.default';
            setValidationError(bus, { property: rule.property, msg: message }, { state, element });
            return false;
          }
          if (rule.path === true && !hasPath(value)) {
            setValidationError(bus, { property: rule.property, msg: rule.msg }, { state, element });
            return false;
          }
        }
      }
    }
  }

  setValidationError(bus, {});
  return true;
};

const extractFontFromQuillClass = (text) => {
  const fonts = [];
  const reg = new RegExp('ql-font-([\\w|-]*)', 'g');
  let matches;
  // eslint-disable-next-line
  while ((matches = reg.exec(text)) !== null) {
    fonts.push(matches[1]);
  }
  return fonts;
};

const normalizeElements = (state) => {
  const { elements } = state.template;
  state.elements = normalizer(elements);
};

const saveRecentlyUsedColors = (state) => {
  const { elements, style } = state.template;
  const colors = [];
  let recentlyUsedColors = lsRetrieve('recentlyUsedColors') || [];
  const tempColors = lsRetrieve('tempRecentColors') || [];
  const IGNORE_COLORS = ['#fff', 'rgba(255, 255, 255, 0)', 'rgb(255, 255, 255)'];
  const noBackgroundElements = ['OmCountdown', 'OmSpacer', 'OmDivider'];

  // teaser
  if (style && style.tab && style.tab.background) {
    const teaserColors = [];
    const teaserBgType = style.tab.background.type;
    const teaserBgColor = style.tab.background.color;
    const teaserBgColor2 = style.tab.background.color2;
    if (teaserBgType !== 'transparent') {
      if (teaserBgColor) teaserColors.push(teaserBgColor);
      if (teaserBgType === 'gradient' && teaserBgColor2) teaserColors.push(teaserBgColor2);
    }
    teaserColors.forEach((teaserColor) => {
      if (typeof teaserColor === 'number') {
        teaserColor = state.template.palette[teaserColor];
      }
      if (teaserColor && IGNORE_COLORS.indexOf(teaserColor) < 0) {
        colors.push(teaserColor);
      }
    });
  }

  // elements
  elements.forEach((element) => {
    if (!noBackgroundElements.includes(element.type) && element.desktop) {
      const elementColors = [];
      const elementBgType = element.desktop.background && element.desktop.background.type;
      const elementBgColor = element.desktop.background && element.desktop.background.color;
      const elementBgColor2 = element.desktop.background && element.desktop.background.color2;
      const elementHoverBgColor = element.desktop.hover && element.desktop.hover.backgroundColor;
      if (elementBgColor && elementBgType !== 'transparent') elementColors.push(elementBgColor);
      if (elementBgType === 'gradient' && elementBgColor2) elementColors.push(elementBgColor2);
      if (elementHoverBgColor) elementColors.push(elementHoverBgColor);

      elementColors.forEach((elementColor) => {
        if (typeof elementColor === 'number') {
          elementColor = state.template.palette[elementColor];
        }
        if (elementColor && IGNORE_COLORS.indexOf(elementColor) < 0) {
          colors.push(elementColor);
        }
      });
    }

    if (element.subElements) {
      // collect coupon colors
      if (element.subElements.couponText) {
        const couponSubelementColor = element.subElements.couponText.desktop.color;
        if (couponSubelementColor) colors.push(couponSubelementColor);
      }
      // collect countdown colors
      if (element.type === 'OmCountdown') {
        const { label, number, separator } = element.subElements;
        const defaultColor = '#000';
        if (separator.desktop.color !== defaultColor) colors.push(separator.desktop.color);
        if (label.desktop.color !== defaultColor) colors.push(label.desktop.color);
        if (number.desktop.background.color !== defaultColor)
          colors.push(number.desktop.background.color);
        if (number.desktop.color !== defaultColor) colors.push(number.desktop.color);
      }
    }
    if (element.data && element.data.text) {
      const quillTextColors = extractTextColorFromQuill(element.data.text);
      if (quillTextColors) colors.push(quillTextColors);
    }
  });

  let allColorsReversed = recentlyUsedColors.concat(colors);
  const mostRecentColors = [];
  tempColors.forEach((item) => {
    if (allColorsReversed.includes(item)) {
      mostRecentColors.push(item);
    }
  });
  allColorsReversed = allColorsReversed
    .concat(mostRecentColors)
    .reverse()
    .filter((x) => IGNORE_COLORS.indexOf(x) < 0);

  recentlyUsedColors = [...new Set(allColorsReversed)].slice(0, MAX_ACCEPTED_RECENTLY_COLORS);
  lsStore(
    'recentlyUsedColors',
    recentlyUsedColors.map((c) => tinyColor(c).toRgbString()),
  );
};

const extractTextColorFromQuill = (text) => {
  const wrapper = document.createElement('div');
  wrapper.innerHTML = text;

  const colors = [];
  wrapper.querySelectorAll('span').forEach((element) => {
    const color = element.style.color;
    if (color) {
      colors.push(color);
    }
  });
  return colors[0];
};

const extractFontStylesFromQuill = (text, fontsAndPropsReturn) => {
  const wrapper = document.createElement('div');
  wrapper.innerHTML = text;

  wrapper.querySelectorAll('em, span').forEach((ele) => {
    const tag = ele.outerHTML;
    const font = extractFontFromQuillClass(tag);
    const isItalic = tag.match(/^<em/);
    const fontWeight = tag.match(/font-weight: (\d+)/);
    const weightAndItalic = `${fontWeight ? fontWeight[1] : '400'}${isItalic ? 'i' : ''}`;

    if (!fontsAndPropsReturn[font[0]]) {
      fontsAndPropsReturn[font[0]] = {};
    }
    if (!fontsAndPropsReturn[font[0]][weightAndItalic]) {
      fontsAndPropsReturn[font[0]][weightAndItalic] = true;
    }
  });

  return fontsAndPropsReturn;
};

const getFontPropsFor = (element, key, fontsAndPropsReturn) => {
  key = key ? `.${key}` : '';
  fontsAndPropsReturn = getFontRelatedProps(element, `desktop${key}`, fontsAndPropsReturn);
  fontsAndPropsReturn = getFontRelatedProps(element, `mobile${key}`, fontsAndPropsReturn);
  return fontsAndPropsReturn;
};

const getFontRelatedProps = (element, key, fontsAndPropsReturn) => {
  const font = _get(element, `${key}.fontFamily`);

  if (font) {
    const weight = _get(element, `${key}.fontWeight`, '400');
    const italic = _get(element, `${key}.fontItalic`, false);
    const weightAndItalic = `${weight}${italic ? 'i' : ''}`;

    if (!fontsAndPropsReturn[font]) {
      fontsAndPropsReturn[font] = {};
    }
    if (!fontsAndPropsReturn[font][weightAndItalic]) {
      fontsAndPropsReturn[font][weightAndItalic] = true;
    }
  }

  return fontsAndPropsReturn;
};

const THEME_FONT_KEY_PRIMARY = 'om-font-1';
const THEME_FONT_KEY_SECONDARY = 'om-font-2';
const THEME_FONT_INDEXES = {
  [THEME_FONT_KEY_PRIMARY]: 0,
  [THEME_FONT_KEY_SECONDARY]: 1,
};

const collectFonts = (template, ignoreGlobalProperties = []) => {
  const fonts = new Set();
  let fontsAndProps = {};
  const globalProperties = [
    'canvas',
    'closeButton',
    'input-picker',
    'inputs',
    'tab',
    'text',
  ].filter((prop) => ignoreGlobalProperties.indexOf(prop) < 0);

  globalProperties.forEach((globalProperty) => {
    const value = _get(template, `style.${globalProperty}.fontFamily`);
    if (value) fonts.add(value);
    fontsAndProps = getFontRelatedProps(template, `style.${globalProperty}`, fontsAndProps);

    if (globalProperty === 'tab') {
      const tabText = _get(template, 'style.data.tabText');
      fontsAndProps = extractFontStylesFromQuill(tabText, fontsAndProps);
      const quillFonts = extractFontFromQuillClass(tabText);
      quillFonts.forEach((f) => fonts.add(f));
    }
  });

  if (template.style.closeButton?.desktop?.fontFamily) {
    const value = _get(template, `style.closeButton.desktop.fontFamily`);
    if (value) fonts.add(value);
    fontsAndProps = getFontRelatedProps(template, `style.closeButton.desktop`, fontsAndProps);
  }
  if (template.style.closeButton?.mobile?.fontFamily) {
    const value = _get(template, `style.closeButton.mobile.fontFamily`);
    if (value) fonts.add(value);
    fontsAndProps = getFontRelatedProps(template, `style.closeButton.mobile`, fontsAndProps);
  }

  // TODO: collect mobile fonts

  for (const e of template.elements) {
    if (e.type === 'OmButton' || e.type === 'OmText') {
      const quillFonts = extractFontFromQuillClass(e.data.text);
      quillFonts.forEach((f) => fonts.add(f));
      fontsAndProps = extractFontStylesFromQuill(e.data.text, fontsAndProps);
    } else if (e.type === 'OmPickAPresent') {
      const title = _get(e, 'desktop.title.fontFamily');
      if (title) fonts.add(title);
      fontsAndProps = getFontPropsFor(e, 'title', fontsAndProps);

      const subtitle = _get(e, 'desktop.subtitle.fontFamily');
      if (subtitle) fonts.add(subtitle);
      fontsAndProps = getFontPropsFor(e, 'subtitle', fontsAndProps);
    } else {
      const value = _get(e, 'desktop.fontFamily');
      if (value) fonts.add(value);
      fontsAndProps = getFontPropsFor(e, '', fontsAndProps);
    }

    if (e.subElements) {
      // eslint-disable-next-line
      Object.keys(e.subElements).forEach((s) => {
        const value = _get(e.subElements[s], 'desktop.fontFamily');
        if (value) fonts.add(value);
        fontsAndProps = getFontPropsFor(e.subElements[s], '', fontsAndProps);
      });
    }
  }

  [THEME_FONT_KEY_PRIMARY, THEME_FONT_KEY_SECONDARY].forEach((fontKey) => {
    const index = THEME_FONT_INDEXES[fontKey];
    const themeFont = template?.themeKit?.fonts?.[index];

    if (!themeFont) return;

    if (fonts.has(fontKey)) {
      fonts.delete(fontKey);
      fonts.add(themeFont);
    }

    if (fontsAndProps?.[fontKey]) {
      fontsAndProps[themeFont] = { ...fontsAndProps[fontKey] };
      delete fontsAndProps[fontKey];
    }
  });

  return { fonts: Array.from(fonts), realUsed: fontsAndProps };
};

const columnSplit = (nr) =>
  Array(nr)
    .fill()
    .map(() => parseInt(GRID_COLUMN_COUNT / nr, 10));
const emptyColumn = () => {
  const column = initializedServices.initElement({
    uid: generateColId(),
    type: 'OmCol',
    pageId: null,
    rowId: null,
    data: {},
  });

  return column;
};
// const emptyColumns = nr => Array(nr).fill().map(() => emptyColumn())
const emptyRow = (coulmnCount) => {
  const row = initializedServices.initElement({
    uid: generateRowId(),
    type: 'OmRow',
    pageId: null,
    // columns: emptyColumns(nr), TODO[peter.jozsa]
    data: {
      columnSplit: columnSplit(coulmnCount),
    },
  });

  return row;
};
const emptyPage = () => {
  const page = initializedServices.initElement({
    uid: generatePageId(),
    type: 'OmPage',
  });

  return page;
};

const _setFloatingActionsPosition = (state, mode) => {
  const newTeaser = state.template.elements.find((e) => e.isTeaser);

  mode = mode || (state.teaserPreview || newTeaser ? 'teaser' : 'overlay');
  let position;
  if (state.mobilePreview) {
    position = 'right';
  } else if (mode === 'overlay') {
    const overlayPosition = state.template.style.overlay.position;
    const isOverlayInRightPosition = [3, 6, 9].includes(overlayPosition);
    position = isOverlayInRightPosition ? 'left' : 'right';
  } else if (newTeaser) {
    position = [3, 6, 9].includes(newTeaser.data.position) ? 'left' : 'right';
  } else {
    const teaserPosition = state.template.style.tab.position;
    position = teaserPosition && teaserPosition.includes('right') ? 'left' : 'right';
  }
  _setEditorAttr(_vm().$bus, 'floatingActionsPosition', position);
};

const _selectPage = (state, pageId) => {
  const pageIdx = state.template.elements.findIndex(
    ({ type, uid }) => type === 'OmPage' && uid === pageId,
  );
  const page = state.template.elements[pageIdx];

  if (!page) {
    return;
  }

  _setFloatingActionsPosition(state, page.isTeaser ? 'teaser' : 'overlay');

  _selectElement(state, page);

  state.selectedPage = page;

  _setEditorAttr(_vm().$bus, 'selectedPage', page);

  _setSpecialElementsOnPage(state);

  if (page.isTeaser) {
    _vm().$nextTick(() => {
      const isNewEditorOrTemplateEditor =
        window.parent.location.href.includes('/edit') ||
        window.parent.location.href.includes('/template-editor');
      if (!isNewEditorOrTemplateEditor) {
        window.parent.om.store.commit('backPane');
      }
    });
  }
};

const _setSpecialElementsOnPage = (state) => {
  _setHasFeedbackOnPage(state);
  _setCouponOnPage(state);
  _setMaxPageHeightIfNeeded(state);
  _couponRedeemShopifyAvailable(state);
};

const _resetEditorPane = (bus, state, paneLevel = 2) => {
  state.quillToolbarInfo.show = false;
  setValidationError(bus, {});
  if (paneLevel) {
    _setEditorAttr(bus, 'paneLevel', paneLevel);
  }
  _deselectAll(state);
  if (window.parent && window.parent.om && window.parent.om.bus)
    window.parent.om.bus.$emit('hideCalendar');
};

const _deselectAll = (state) => {
  if (window.parent && window.parent.om && window.parent.om.bus) {
    window.parent.om.bus.$emit('hideColorPicker');
    window.parent.om.bus.$emit('closeSidebarPopover');
  }

  if (state) {
    state.selectedRow = null;
    state.selectedColumn = null;
    state.selectedElement = null;
  }
  window.parent.om.bus.$emit('showLayers');
};

const _replaceCopiedStyleElement = (state, element) => {
  const clipboardStyle = state.clipboardStyle;
  if (clipboardStyle?.tiptap) {
    const { mergedStyles } = clipboardStyle;
    _emit(window.om.store._vm.$bus, 'applyCopyStyle', mergedStyles);
    return;
  }
  const { uid } = element;
  const hasSmartTag = !!element.data.text?.includes('class="smart-tag"');

  if (
    clipboardStyle.uid !== uid &&
    clipboardStyle &&
    clipboardStyle.type === element.type &&
    !hasSmartTag
  ) {
    const elementsStyleProperty = getStylePropertyFromType(element.type);
    elementsStyleProperty.forEach((prop) => {
      if (prop === QUILL_EXCEPTION) {
        const replacedHTML = replaceQuillContent(element.data.text, clipboardStyle[prop]);
        _set(element, prop, replacedHTML);
      } else {
        const copiedStyle = _get(clipboardStyle, prop);
        _set(element, prop, copiedStyle);
      }
    });
    track('editor_click', {
      location: 'workspace',
      component: element.type,
      setting: 'copy-style',
      change: `paste:${element.type}`,
      pageName: state.selectedPage?.data?.title ?? 'Unknown',
      device: state.mobilePreview ? 'mobile' : 'desktop',
    });
    _emit(window.om.store._vm.$bus, 'updateElementStyle', element.uid);
    state.clipboardStyle = {};
  }
};

const _selectElement = (state, { uid: id }) => {
  _deselectAll(state);

  id = id.uid || id; // TODO investigate why the setValidationError gives the id like {uid: id} end gives only for new template?
  const elementIdx = state.template.elements.findIndex(({ uid }) => uid === id);
  if (elementIdx === -1) {
    console.error(new Error('Tried to select not existing element: ', JSON.stringify(id)));
    return;
  }
  const element = state.template.elements[elementIdx];
  _replaceCopiedStyleElement(state, element);
  const { type, rowId, colId } = element;
  let row = null;
  let col = null;
  if (rowId) {
    row = state.template.elements.find(({ uid, type }) => type === 'OmRow' && uid === rowId);
  }
  if (colId) {
    col = state.template.elements.find(({ uid, type }) => type === 'OmCol' && uid === colId);
  }

  const { driver } = _editorStore().state.productTour;
  if (
    driver &&
    driver.hasHighlightedElement() &&
    element.type !== 'OmPage' &&
    !state.mobilePreview
  ) {
    if (driver.getHighlightedElement().node.classList.contains('iframe-fake')) {
      _emit(window.om.store._vm.$bus, 'advanceProductTour');
      _emit(window.om.store._vm.$bus, 'toggleHighlight', '.sidebar');
    }
  }
  if (element.type !== 'OmPage') {
    _emit(window.om.store._vm.$bus, 'openRightSidebar', true);
  }
  state.selectedElement = element;

  if (type === 'OmCol') {
    state.selectedColumn = element;
    state.selectedRow = row;
  } else if (type === 'OmRow') {
    state.selectedRow = element;
  } else if (type !== 'OmPage') {
    state.selectedColumn = col;
    state.selectedRow = row;
  }
};

const _selectElementByUid = (state, uid) => _selectElement(state, { uid });

const getPageElementsByType = (state) => {
  const pages = [];
  const rows = [];
  const columns = [];
  const elements = [];

  for (const e of state.template.elements) {
    if (e.isTeaser) {
      continue;
    }

    if (e.type === 'OmPage') {
      pages.push(e);
    } else if (e.type === 'OmRow') {
      rows.push(e);
    } else if (e.type === 'OmCol') {
      columns.push(e);
    } else {
      elements.push(e);
    }
  }

  return {
    pages,
    rows,
    columns,
    elements,
  };
};

const findPageIndex = (state, uid) => {
  let pageIndex = -1;
  const { pages } = getPageElementsByType(state);

  for (const e of state.template.elements) {
    if (e.uid === uid) {
      pageIndex = pages.findIndex((page) => page.uid === e.pageId);
      break;
    }
  }

  return pageIndex;
};

const findBox = (state, uid) => {
  return state.template.elements.find((e) => e.uid === uid);
};

const _clearBoxStyles = (state, clonedState) => {
  const stateRef = clonedState || state;

  stateRef.template.elements.forEach((el) => {
    if (el.type !== 'OmPage') {
      Vue.set(el, 'selected', false);
    }
  });
};

const _createElement = (state, type, rawElement, overrides) => {
  const raw = merge(
    {
      uid: generateId(type),
      type,
      data: {},
      desktop: {},
      mobile: {},
    },
    rawElement,
  );

  if (!overrides) {
    // DO NOT REMOVE _clone OR elementDefaults will be Observable and
    // lots of reactiveSetters will be triggered causing freeze on the main thread.
    const defaults = state.template.elementDefaults[type];
    if (defaults) {
      overrides = _clone(defaults);
    }
  }

  const element = initializedServices.initElement(raw, overrides);

  initializedServices.postInitElement(element, state.template, true, state.campaign.version);

  return element;
};

const overrideButtonDefaults = (defaults) => {
  for (const type in defaults) {
    if (type === 'OmButton' && defaults[type].data.action === 'redirect') {
      defaults[type].data.action = 'nextPopup';
      defaults[type].data.redirectUrl = '';
    }
  }
};

const _updateThemeKit = async (template) => {
  const { themeKit } = template;
  if (!themeKit) return;

  const editorStore = _editorStore();
  const themeKitName = themeKit?.name;
  const themeKitId = themeKit?.id;
  const themeKitFromServer = await _getCustomThemeKit({ themeKitName, themeKitId });
  if (!themeKitFromServer) return;

  const { themeKit: _currentThemeKit, source: sourceThemeKit, logo, id } = themeKitFromServer;

  if (logo?.history?.length) {
    logo.history = logo.history.filter(Boolean);
  }

  const currentThemeKit = { ..._currentThemeKit, logo, id };

  const { hidden = false } = currentThemeKit;
  delete currentThemeKit.hidden;
  editorStore.commit('setIsThemekitHidden', hidden);
  _editorStore().commit(
    'setSavedThemeKitSettings',
    JSON.parse(JSON.stringify({ ...currentThemeKit, logo, id })),
  );
  editorStore.commit('setThemeKitSettings', currentThemeKit);
  editorStore.commit('setSourceThemeKit', sourceThemeKit);

  const newThemeKit = {
    rounding: currentThemeKit.rounding,
    fonts: currentThemeKit.fonts,
    name: currentThemeKit.name,
    logo,
    id,
  };
  if (themeKitId) {
    newThemeKit.id = themeKitId;
  }

  template.themeKit = newThemeKit;
};

const calculateCustomJSByEvents = (customJsByEvents, customJs) => {
  customJsByEvents = customJsByEvents || {
    pageLoad: '',
    popupShow: '',
    popupFill: '',
    popupReject: '',
  };
  if (customJs && customJs.length && !customJsByEvents.pageLoad) {
    customJsByEvents.pageLoad = customJs;
  }

  return customJsByEvents;
};

const queryTemplateOrCampaign = async (state, isTemplateEditorMode, variantId) => {
  const templateQuery = gql`{template(input: {name: "${variantId}"}){_id, name, template, locale, universal, theme}}`;
  const variantQuery = gql`{template: variant(_id: "${variantId}"){template, campaignInnerId, campaignId, campaignName, templateName, device, domain, omUrl, variantId, variantName, lang, poweredBy { visible, text, link }, databaseId, universal, theme, version, isNewCampaign}}`;
  const response = await _apollo(state).query({
    query: isTemplateEditorMode ? templateQuery : variantQuery,
  });
  const template = response.data.template;
  if (!template) throw Error('template not present');
  return template;
};

const _afterTemplateOrCampaignLoaded = (vm, state, dispatch, commit, setWorkspaceAttr) => {
  vm.$nextTick(() => {
    if (state.docker) console.log('setting load complete');
    replaceCss(state.template, false, state.docker);
    CssGenerator.generateElementStyles(state.template, dispatch, state.docker);
    state.loadComplete = true;
    dispatch('setSpecialElementsOnPage');
    dispatch('setImageSrcsOnFirstPage');
    disableLinkClicks();
    if (vm.$bus) {
      vm.$bus.$on('initV2Teaser', () => {
        dispatch('initV2Teaser').then(() => {
          const teaser = state.template.elements.find((e) => e.isTeaser);
          setWorkspaceAttr('teaserPageAdded', teaser.uid);
        });
      });
      vm.$bus.$on('checkShopConnectionResult', ({ shopify, shoprenter }) => {
        commit('isShopifyActive', shopify);
        commit('isShoprenterActive', shoprenter);
      });
      vm.$bus.$on('updateMaxPageHeight', () => _setMaxPageHeightIfNeeded(state));
      vm.$bus.$on('getWrapperWidth', (id) => {
        const item = document.querySelector(`#${id}`);
        if (item) {
          let { fontSize } = getComputedStyle(item);
          fontSize = parseInt(fontSize, 10);
          const wrapper = item.closest(`[data-margin="${id.replace('ele_', 'ele_ma_')}"]`);
          const itemWidth = Math.floor(wrapper.getBoundingClientRect().width);
          vm.$bus.$emit('wrapperWidth', {
            id,
            itemWidth,
            currentFontSize: fontSize,
          });
        }
      });
      vm.$bus.$on('initProductTour', (driver) => {
        [
          ...document.querySelectorAll('*[id^="pge_"]'),
          document.querySelector('.om-popup-close'),
        ].forEach((element) =>
          element.addEventListener('click', () => {
            if (driver && driver.hasHighlightedElement()) {
              setWorkspaceAttr('advanceProductTour');
              setWorkspaceAttr('toggleHighlight', '.sidebar');
            }
          }),
        );
      });
      vm.$bus.$on('initFullscreen', (value) => {
        commit('initFullscreen', value);
      });
    }
    setWorkspaceAttr('checkShopConnection');
  });
};

Vue.use(Vuex);
export default new Vuex.Store({
  modules: {
    customTheme,
    wysiwyg: wysiwygStore,
  },
  state: {
    phoneFrameStyles: {
      transform: '',
      top: '',
    },
    isResizedMobileEditor: false,
    selectedElement: null,
    selectedColumn: null,
    selectedRow: null,
    selectedPage: null,
    lastInsertedElementId: null,
    scaledPreview: true,

    isAdmin: false,
    editMode: true,
    isSubUser: false,
    whiteLabel: null,
    tempLang: 'en',
    databaseId: null,
    theme: null,
    isShopifyActive: false,
    isShoprenterActive: false,
    clipboardStyle: {},
    baseImages: [],
    ppoVariableNames: [],
    template: {
      uid: `tmp_${nanoid(9)}`,
      features: [],
      backgroundAnimation: 'none',
      elementDefaults: {},
      elements: [],
      data: {
        closeGestures: _clone(closeGesturesDef),
        isPermanentTeaser: true,
      },
      style: {
        palette: {
          mainColor: '#AA3939',
          secondaryColors: ['#FFAAAA', '#D46A6A', '#801515', '#550000'],
        },
        text: {
          size: null,
          globalWidth: 110,
          fontFamily: null,
          fontSize: 16,
          defaultFontSize: 16,
          textAlign: 'center',
          color: '#000',
          border: {
            selectedBorder: 'no',
            type: 'solid',
            width: 2,
            radius: [null, null, null, null],
            radiusChained: true,
            color: 0,
          },
          manualPadding: {
            allSides: true,
            top: 5,
            left: 5,
            bottom: 5,
            right: 5,
          },
          manualMargin: {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          },
          textShadow: {
            type: 'none',
            color: '#000',
          },
          // background: {
          //   color: -1,
          //   color2: '#fff',
          //   type: 'solid-fill',
          //   gradientType: 'linear',
          //   gradientCenter: 'circle at center',
          //   linearDirection: 45,
          //   transparency: 1,
          //   imageId: '',
          //   imagePosition: 'cover',
          //   imageRepeat: 'no-repeat',
          //   resizeType: 'auto'
          // }
        },
        button: {
          textJustify: 'om-justify-center',
          size: 'fluid',
          fontSize: 16,
          defaultFontSize: 16,
          fontFamily: null,
          color: '#fff',
          globalWidth: 150,
          globalHeight: 45,
          shadow: {
            type: 'none',
            color: '#000',
          },
          manualPadding: {
            allSides: false,
            top: 15,
            left: 30,
            bottom: 15,
            right: 30,
          },
          manualMargin: {
            top: 5,
            right: 0,
            bottom: 5,
            left: 0,
          },
          background: {
            color: -1,
            color2: '#fff',
            type: 'solid-fill',
            gradientType: 'linear',
            gradientCenter: 'circle at center',
            linearDirection: 45,
            transparency: 1,
            imageId: '',
            imagePosition: 'cover',
            imageRepeat: 'no-repeat',
            resizeType: 'auto',
          },
          hover: {
            fontColor: '#fff',
            borderColor: 3,
            backgroundColor: 3,
          },
          border: {
            selectedBorder: 'no',
            type: 'solid',
            width: 2,
            radius: [null, null, null, null],
            radiusChained: true,
            color: 0,
          },
        },
        closeButton: {
          borderRadius: 0,
          fontSize: 25,
          color: '#000',
          background: '#ed5a29',
          delay: 1,
          desktop: {
            right: 0,
            top: 0,
          },
          mobile: {
            right: null,
            top: null,
          },
          fontFamily: 'gruppo',
        },
        teaserCloseButton: {
          borderRadius: 10,
          fontSize: 15,
          color: '#000',
          background: '#f3f5f8',
          margin: {
            allSides: true,
            left: 8,
            right: 8,
            top: 8,
            bottom: 8,
          },
          fontFamily: 'gruppo',
        },
        canvas: {
          fontFamily: 'open-sans',
          minheight: 300,
          width: 600,
        },
        countdown: {
          manualMargin: {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          },
        },
        tab: {
          fontSize: 16,
          fontFamily: null,
          color: '#696d72',
          positions: {
            position: 'top-left',
          },
          display: {
            before: false,
            after: false,
          },
          type: {
            size: 60,
            style: 'corner',
          },
          border: {
            selectedBorder: 'full',
            type: 'solid',
            color: 0,
            width: 2,
            radius: [null, null, null, null],
            radiusChained: true,
          },
          shadow: {
            type: 'none',
            color: '#000',
          },
          background: {
            color: '#fff',
            color2: -1,
            type: 'solid-fill',
            gradientType: 'linear',
            gradientCenter: 'circle at center',
            linearDirection: 45,
            transparency: 1,
            imageId: '',
            imagePosition: 'cover',
            imageRepeat: 'no-repeat',
            resizeType: 'auto',
          },
          animation: {
            switch: {
              type: '',
            },
            attentionSeeker: {
              type: '',
              freq: 3,
            },
          },
        },
        video: {
          manualMargin: {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          },
        },
        social: {
          manualMargin: {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          },
        },
        inputs: {
          textJustify: 'om-justify-center',
          size: '100w',
          fontSize: 16,
          lineHeight: 16,
          globalHeight: 45,
          globalWidth: 100,
          fontFamily: null,
          color: '#696d72',
          fontItalic: false,
          textWeight: false,
          textDecoration: false,
          background: {
            color: '#fff',
            color2: -1,
            type: 'solid-fill',
            gradientType: 'linear',
            gradientCenter: 'circle at center',
            linearDirection: 45,
            transparency: 1,
            imageId: '',
            imagePosition: 'cover',
            imageRepeat: 'no-repeat',
            resizeType: 'auto',
          },
          manualMargin: {
            top: 5,
            bottom: 5,
            left: 0,
            right: 0,
          },
          border: {
            selectedBorder: 'full',
            type: 'solid',
            color: 0,
            width: 2,
            radius: [null, null, null, null],
            radiusChained: true,
          },
          shadow: {
            type: 'none',
            color: '#000',
          },
        },
        'input-picker': {
          textJustify: 'om-justify-center',
          size: '100w',
          fontSize: 16,
          lineHeight: 16,
          textAlign: 'left',
          globalHeight: 45,
          globalWidth: 100,
          fontFamily: null,
          color: '#696d72',
          orientation: false,
          fontItalic: false,
          textWeight: false,
          textDecoration: false,
          background: {
            color: '#fff',
            color2: -1,
            type: 'solid-fill',
            gradientType: 'linear',
            gradientCenter: 'circle at center',
            linearDirection: 45,
            transparency: 1,
            imageName: '',
            imagePosition: 'cover',
            imageRepeat: 'no-repeat',
            resizeType: 'auto',
          },
          manualMargin: {
            top: 5,
            bottom: 5,
            left: 0,
            right: 0,
          },
          border: {
            selectedBorder: 'full',
            type: 'solid',
            color: 0,
            width: 2,
            radius: [null, null, null, null],
            radiusChained: true,
          },
          shadow: {
            type: 'none',
            color: '#000',
          },
        },
        feedback: {
          textJustify: 'om-justify-center',
          size: '100w',
          fontSize: 16,
          lineHeight: 16,
          globalHeight: 45,
          globalWidth: 100,
          fontFamily: null,
          color: '#696d72',
          orientation: false,
          fontItalic: false,
          textWeight: false,
          textDecoration: false,
          background: {
            color: '#fff',
            color2: -1,
            type: 'solid-fill',
            gradientType: 'linear',
            gradientCenter: 'circle at center',
            linearDirection: 45,
            transparency: 1,
            imageName: '',
            imagePosition: 'cover',
            imageRepeat: 'no-repeat',
            resizeType: 'auto',
          },
          manualMargin: {
            top: 5,
            bottom: 5,
            left: 0,
            right: 0,
          },
          border: {
            selectedBorder: 'full',
            type: 'solid',
            color: 0,
            width: 2,
            radius: [null, null, null, null],
            radiusChained: true,
          },
          shadow: {
            type: 'none',
            color: '#000',
          },
        },
        image: {
          origin: '',
          size: 'manual',
          height: null,
          minWidth: '0',
          align: 'center',
          targetUrl: 'https://www.google.com',
          imageTarget: '_blank',
          manualMargin: {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          },
        },
        overlay: {
          background: {
            ...defaultOverlayBackground,
          },
          manualMargin: {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
          },
          position: 5,
        },
        animation: {
          type: 'no-animation',
          inactivityPeriod: null,
          scrollingPercentage: null,
          min: null,
          sec: null,
          backgroundAnimation: 'none',
        },
        background: {
          gradientCenter: 'circle at top',
          imageId: '',
        },
        ribbon: false,
        ribbonSettings: {
          backgroundColor: 'rgb(208, 27, 27)',
          color: '#fff',
          text: 'Hi!',
        },
        customHTML: {
          size: '100w',
          textAlign: 'left',
          textJustify: 'om-justify-center',
          globalHeight: 400,
          globalWidth: 400,
          manualMargin: {
            top: 5,
            bottom: 5,
            left: 0,
            right: 0,
          },
        },
        mode: 'popup', // nano, popup
        data: {
          textJustify: 'om-justify-center',
          tabText: null,
        },
      },
      pages: [emptyPage()],
      customCss: null,
      customJs: null,
      customJsByEvents: {
        pageLoad: '',
        popupShow: '',
        popupFill: '',
        popupReject: '',
      },
      images: [],
      inputs: [],
      scratchCardOptions: _clone(scratchOptionsDef),
      pickAPresentOptions: _clone(pickAPresentOptionsDef),
      wheelOptions: [
        {
          isActive: true,
          weight: 1,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: false,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: true,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: false,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: true,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: false,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: true,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: false,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: true,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: false,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: true,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
        {
          isActive: false,
          weight: 0,
          chanceToWin: 0,
          title: '',
          couponCode: '',
        },
      ],
      mockSiteLayout: DefaultMockLayout,
      inlineSvgs: {},
      universal: false,
      themeKit: {
        name: '',
        rounding: '',
        fonts: [],
      },
    },
    templateSaveData: null,
    docker: false,
    previewTokenSet: false,
    mobilePreview: false,
    dragInfo: null,
    dropLocation: null,
    quillToolbarInfo: {
      show: false,
      toolbarSelector: null,
      uppercase: false,
    },
    campaign: {},
    loadComplete: false,
    poweredBy: null,
    updatedVariantName: null,
    usedFonts: null,
    realUsedFonts: null,
    loadedFonts: [],
    workspaceFontsLoaded: false,
    fonts: null,
    teaserPreview: false,
    hasFeedbackOnPage: true,
    hasRadioFeedback: true,
    imageSrcsOnFirstPage: [],
    teaserSelected: false,
    hasCouponOnPage: false,
    maxPageHeight: null,
    variantId: null,
    templateHash: null,
    confirmedProductsMissing: false,
    accountFeatures: [],
    isNewCampaign: false,
    hoverbarActive: false,
  },
  getters: {
    page: (state) => _page(state),
    boxes: (state) => _clone(_boxes(state)),
    elements: (state) => _clone(state.template.elements),
    rowsOfPage: (state) => (pageId) =>
      state.template.elements.filter((e) => e.type === 'OmRow' && e.pageId === pageId),
    columnsOfRow: (state) => (rowId) =>
      state.template.elements.filter((e) => e.type === 'OmCol' && e.rowId === rowId),
    elementsOfColumn: (state) => (colId) =>
      state.template.elements.filter(
        (e) =>
          e.type !== 'OmCol' &&
          e.type !== 'OmRow' &&
          e.type !== 'OmPage' &&
          !e.isTeaser &&
          e.colId === colId,
      ),
    selectedColumnId: (state, { selectedColumn }) => (selectedColumn ? selectedColumn.uid : null),
    findImage: (state) => (imageId) => _findImage(state, imageId),
    products: (state) => state.selectedElement.data.products,
    getGlobalStyle: (state) => state.template.style,
    isNano: (state) => _isNano(state),
    isSidebar: (state) => _isSidebar(state),
    isEmbedded: (state) => _isEmbedded(state),
    isPopup: (state) => !_isNano(state) && !_isSidebar(state) && !_isEmbedded(state),
    isTemplateEditorMode: (state) =>
      state.templateSaveData &&
      (state.templateSaveData.type === undefined || state.templateSaveData.type === 'base'),
    isInterstitial: (state) => state.template.style.mode === 'interstitial',
    isFullscreen: (state) => state.template.style.mode === 'fullscreen',
    isFullHeightSidebar: (state) => state.template.style.overlay.fitToScreen === true,
    hasRibbon: (state) => state.template.style.ribbon && state.template.style.ribbon.show,
    rowIds: (state) => _boxes(state).map((row) => row.uid),
    hasWheel: (state) => {
      const hasWheel = state.template.elements.some(({ type }) => type === 'OmLuckyWheel');

      _setEditorAttr(window.om.store._vm.$bus, 'hasWheel', hasWheel);

      return hasWheel;
    },
    isNewWheelLayout: (state) => {
      const wheel = state.template.elements.find(({ type }) => type === 'OmLuckyWheel');

      return _get(wheel, 'mobile.position', false) === 'bottom';
    },
    pages: (state) => state.template.elements.filter((e) => isPage(e)),
    allPagesRaw: (state) => state.template.elements.filter((e) => e.type === ELEMENTS.OmPage),
    pageCount: (_, getters) => getters.pages.length,
    hasButtonOnPage: (state) => _hasButtonOnPage(state),
    validateButtonOnPage: (state) => _validateButtonOnPage(state, state.selectedPage.uid),
    installedFonts: (state) => {
      const fonts = [];
      if (state.fonts) {
        Object.keys(state.fonts).forEach((key) => {
          const font = state.fonts[key];
          if (font.preInstalled || font.installedSubsets || font.custom) {
            fonts.push({
              key,
              ...font,
            });
          }
        });
      }
      fonts.sort((a, b) => a.family.toLowerCase().localeCompare(b.family.toLowerCase()));
      return fonts;
    },
    brandName: (state) =>
      state.whiteLabel ? state.whiteLabel.brandName || 'OptiMonk' : 'OptiMonk',
    defRedirectUrl: (state) =>
      state.whiteLabel
        ? `https://${state.whiteLabel.domainBase}` || 'https://www.optimonk.com'
        : 'https://www.optimonk.com',
    campaignInnerId: (state) => state.campaign.campaignInnerId,
    mockSiteLayout: (state) => state.template.mockSiteLayout || DefaultMockLayout,
    // prettier-ignore
    getStyleProperty: (state) => ({ uid, property }) => {
      const element = findBox(state, uid);

      if (element) {
        const device = state.mobilePreview ? 'mobile' : 'desktop';
        return _get(element[device], property);
      }
    },
    inEditor: (state) => state.editMode || state.mobilePreview,
    teaserPage: (state) => state.template.elements.find((e) => e.isTeaser),
    isTeaserPage: (state) =>
      (state.selectedPage && state.selectedPage.isTeaser) || state.teaserPreview,
    getTemplateFeatures: (state) => state.template.features,
    hasTemplateFeature: (state) => (given) =>
      state.template.features &&
      Array.isArray(state.template.features) &&
      state.template.features.includes(given),
    floatingImages: (state) => state.template.elements.filter((e) => e.type === 'OmFloatingImage'),
    // eslint-disable-next-line
    hasAccountFeature: (state) => (given) => isFeatureEnabled(state.accountFeatures, given),
    featureGenerateWithAI: (state, getters) => () => getters.hasAccountFeature(CONTENT_BY_AI),
    isNewCampaign: (state) => {
      return state.isNewCampaign;
    },
    isVisualizedMarginsEnabled: (_, getters) => getters.hasAccountFeature(VISUALIZED_MARGINS),
    isEditorTipTapTextEnabled: (state) => isEditorTipTapTextEnabled(state.accountFeatures, router),
    isEditorTipTapButtonEnabled: (state) =>
      isEditorTipTapButtonEnabled(state.accountFeatures, router),
  },
  actions: {
    async validateElements({ state }, pageId = 'all') {
      return validateElements(state, this._vm.$bus, pageId);
    },
    isImageUsedElsewhere({ state, getters }, imageData) {
      const replacementTargets = reduceElements({
        state,
        pages: getters.allPagesRaw,
        imageData,
      });

      return replacementTargets.length > 1;
    },
    changeImageOnPages(
      { state, getters, dispatch },
      { type, imageData, shouldResetVisibility = false, whitelist },
    ) {
      const selectedPage = state.selectedPage;
      const isCurrentPageOnly = type === 'currentPage';
      const pages = isCurrentPageOnly ? [selectedPage] : getters.allPagesRaw;

      replaceImages({ state, pages, imageData, dispatch, shouldResetVisibility, whitelist });
    },
    async saveTemplateWeb(context, data) {
      const isCustomThemeNameError = _editorStore().state.isCustomThemeNameError;
      if (isCustomThemeNameError === true) {
        console.log('invalid settings, not saving');
        window.parent.om.bus.$emit('show-left-sidebar-content', {
          buttonIndex: 0,
          withoutCloseCase: true,
        });
        return;
      }

      const valid = await validateElements(context.state, this._vm.$bus);
      if (!valid) {
        console.log('invalid settings, not saving');
        return;
      }
      normalizeElements(context.state);
      saveRecentlyUsedColors(context.state);

      const clonedState = _clone(context.state);
      clonedState.template.inlineSvgs = {}; // clear svgs from template
      const { images, inputs } = collectElementsAllPage(context.state);
      clonedState.template.inputs = inputs;
      clonedState.template.images = clonedState.template.images.filter(
        (i) => images.indexOf(i._id) > -1,
      );
      clonedState.template.images = clonedState.template.images.filter((image, index, self) => {
        return self.findIndex((t) => t._id === image._id) === index;
      });

      context.dispatch('saveMostUsedFonts', clonedState.template);
      const automaticCouponComponents = clonedState.template.elements.filter(
        (element) =>
          element.type === 'OmCoupon' && element.data.coupon.type === 'shopify_automatic',
      );
      if (!context.getters.isTemplateEditorMode && automaticCouponComponents.length) {
        context.dispatch('saveAutomaticCouponConfig', { elements: automaticCouponComponents });
      }

      const isUniversal = context.state?.template?.universal;
      const { themeKit } = clonedState.template;
      if (isUniversal && themeKit) {
        themeKit.colors = clonedState.template.style.palette;
      }

      _setEditorAttr(this._vm.$bus, 'templateHash', context.state.templateHash);
      _setEditorAttr(this._vm.$bus, 'saveCompleted', false);
      const { type, value } = context.state.templateSaveData;
      let mutation;
      if (type === 'base') {
        // prettier-ignore
        mutation = {
          mutation: gql`
            mutation($name: String!, $template: String!) {
              upsertTemplate(input: { name: $name, template: $template })
            }
          `,
          variables: { name: value, template: JSON.stringify(clonedState.template) },
        };
      } else {
        const variables = {
          _id: value,
          template: JSON.stringify(clonedState.template),
        };
        if (context.state.updatedVariantName) variables.name = context.state.updatedVariantName;
        mutation = {
          // prettier-ignore
          mutation: gql`
            mutation($_id: String!, $template: String!, $name: String) {
              updateVariant(
                input: { _id: $_id, template: $template, name: $name }
              )
            }
          `,
          variables,
        };
      }
      client.mutate(mutation).then((r) => {
        if (!r.data.error) {
          _setEditorAttr(this._vm.$bus, 'saveCompleted', true);
        }

        if (isUniversal && themeKit) {
          window.parent.om.bus.$emit('saveThemeKit');
        } else {
          _emit(this._vm.$bus, 'templateSaved', data);
        }
      });
    },
    selectElement({ commit }, { uid, target }) {
      commit('setSelectedElement', { uid, target });
    },
    getFonts({ state }) {
      return new Promise((resolve, reject) => {
        _apollo(state)
          .query({
            query: GET_FONTS,
          })
          .then(({ data }) => {
            state.fonts = _clone(data.fonts);
            _emit(this._vm.$bus, 'setFonts', data.fonts);
            _emit(this._vm.$bus, 'generateCss');

            // Refresh fonts in quill
            if (['OmText', 'OmButton'].includes(state.selectedElement?.type)) {
              const uid = state.selectedElement?.uid;
              // simple _selectElement not enough
              _deselectAll(state);
              this._vm.$nextTick(() => {
                _selectElement(state, { uid });
              });
            }
            resolve();
          })
          .catch((e) => reject(e));
      });
    },
    async loadTemplateWeb({ state, dispatch, commit, getters }, payload) {
      const bus = this._vm.$bus;
      const setEditorAttr = _setEditorAttrFactory(bus);
      const setWorkspaceAttr = _setWorkspaceAttrFactory(bus);

      const { type, value: variantId } = payload;
      const isTemplateEditorMode = type === 'base';
      state.variantId = variantId;
      state.templateSaveData = payload;
      setEditorAttr('templateSaveData', state.templateSaveData);

      // van még ilyen? (variáns id nyelvi értéket tartalmaz?)
      const defaultLang =
        isTemplateEditorMode && variantId && variantId.toLowerCase().endsWith('_hu') ? 'hu' : 'en';

      try {
        const response = await queryTemplateOrCampaign(state, isTemplateEditorMode, variantId);
        state.databaseId = response.databaseId;

        if (response.theme) {
          state.theme = response.theme;
        }

        if (response.campaignName) {
          state.campaign = response;
          setEditorAttr('campaign', response);
        }

        state.tempLang = response.locale || response.lang || 'en';
        setWorkspaceAttr('changeLocale', response.locale || response.lang || defaultLang);

        let template = JSON.parse(response.template);

        template.inlineSvgs = await getSvgsAsDataFrom(template.images);

        const templateName = isTemplateEditorMode ? response.name : response.templateName;
        const tplVersion = parseInt(template.__version, 10) || 0;

        if (tplVersion === 0) {
          template = templateInitializer(template, {
            ...state.whiteLabel,
            isSubUser: state.isSubUser,
          });
        }

        if (tplVersion < 2) {
          if (!state.docker && !isTemplateEditorMode) {
            // ez kell még?
            sendTemplateBackupRequest(
              state.databaseId,
              response.campaignInnerId,
              state.variantId,
              _clone(template),
              'save',
            );
          }
          initializedServices.migrators(template, 1);
          template.__version = '2.0.0';
        }

        await _handleTemplateFonts({ template, state, commit, dispatch });

        // Hack for OMSC-1045 existing templates button defaults
        overrideButtonDefaults(template.elementDefaults);

        initializedServices.initializers(template, templateName, response.version);
        initializedServices.migrators(template, 2);

        if (!template.style.palette.themeColors) {
          template.style.palette.themeColors = [];
        }

        if (!template.style.themeSettings) {
          template.style.themeSettings = {};
        }

        if (!template.customCss) {
          template.customCss = '';
        }

        template.customJsByEvents = calculateCustomJSByEvents(
          template.customJsByEvents,
          template.customJs,
        );

        state.template = template;

        setEditorAttr('template', template);

        if (response.device && response.device === 'mobile') {
          state.mobilePreview = true;
          setEditorAttr('mobilePreview', state.mobilePreview);
        }

        if (getters.hasTemplateFeature(TEMPLATE_FEATURES.NEW_TEASER)) {
          dispatch('initV2Teaser');
        }

        setWorkspaceAttr('sendGlobalStyle', template.style);

        _emit(this._vm.$bus, 'setImages', template.images);

        setEditorAttr('inputs', template.inputs, []);
        setEditorAttr('wheelOptions', template.wheelOptions, []);
        setEditorAttr('scratchCardOptions', template.scratchCardOptions, scratchOptionsDef);
        setEditorAttr('pickAPresentOptions', template.pickAPresentOptions, pickAPresentOptionsDef);
        setEditorAttr('customCss', template.customCss);
        setEditorAttr('customJsByEvents', template.customJsByEvents);
        setEditorAttr('data', template.data);
        setEditorAttr('mockSiteLayout', template.mockSiteLayout);

        const pages = template.elements.filter((e) => isPage(e));
        setWorkspaceAttr('setPages', pages);
        state.selectedPage = pages[0];

        commit('setFloatingActionsPosition');

        const clonedState = _clone(state);
        _clearBoxStyles(state, clonedState);

        setEditorAttr('templateHash', objectHash(clonedState.template));

        state.poweredBy = response.poweredBy;
        state.isNewCampaign = response.isNewCampaign;

        const universal = response.universal;
        state.template.universal = universal;
        setEditorAttr('universal', universal);

        if (universal && !state.docker) {
          await _updateThemeKit(template);

          state.baseImages = _clone(template.images);
          dispatch('customTheme/storeElementsWithUid');
          setWorkspaceAttr('endLoadTemplate', true);
        }

        _afterTemplateOrCampaignLoaded(this._vm, state, dispatch, commit, setWorkspaceAttr);

        if (!state.docker) {
          state.ppoVariableNames = await _getPPOVariableNames(state.campaign.domain);
        }
      } catch (e) {
        if (state.docker) console.log('load error');
        console.log('err', e);
        setWorkspaceAttr('changeLocale', defaultLang);
        setWorkspaceAttr('showAdminLoader', false);
        setWorkspaceAttr('endLoadTemplate', false);
      }
      _emit(this._vm.$bus, 'templateLoaded');
    },
    replaceTemplate({ state, getters }, { template, selectedPageId, selectedElementId }) {
      state.loadComplete = false;

      let selectedElement = selectedElementId
        ? template.elements.find(({ uid }) => uid === selectedElementId)
        : null;
      let selectedPage = selectedPageId
        ? template.elements.find(({ uid }) => uid === selectedPageId)
        : null;

      if (!selectedElement) {
        if (state.selectedElement) {
          // if selectedElementId was not provided and current state has selectedElement,
          // then we need to find the new reference of the selectedElement. If new template
          // does not contain the selectedElement then we need to deselect it.
          selectedElement = template.elements.find(({ uid }) => uid === state.selectedElement.uid);
        }
      }
      if (!selectedPage) {
        if (state.selectedPage) {
          // if selectedPageId was not provided and current state has selectedPage(ofc. we have),
          // then we need to find the new reference of the selectedPage.
          selectedPage = template.elements.find(({ uid }) => uid === state.selectedPage.uid);
        }
      }

      state.template = template;

      if (selectedElement) {
        _selectElement(state, { uid: selectedElement.uid });
      } else {
        _deselectAll(state);
      }

      if (!selectedPage) {
        // selectedPage does not exist in new template, we select first page
        selectedPage = state.template.elements.find(({ type }) => type === 'OmPage');
      }

      _selectPage(state, selectedPage.uid);
      _emit(this._vm.$bus, 'setPages', getters.pages);

      // Needed to not trigger history save
      this._vm.$nextTick(() => {
        state.loadComplete = true;
      });
    },
    async loadInputFields({ commit, state }) {
      const { type, value } = state.templateSaveData;

      const templateQuery = gql`{fields: allTemplateFields(template: "${value}"){_id, customId ,name, type}}`;
      const variantQuery = gql`
        {
          fields: allFields {
            _id
            customId
            name
            type
          }
        }
      `;

      try {
        const {
          data: { fields },
        } = await apolloClient.query({
          query: type === 'base' ? templateQuery : variantQuery,
        });

        const inputs = addInputDefaultOptions(_clone(fields), state.selectedElement);

        commit('addFields', inputs);
      } catch (e) {
        console.log(e);
      }
    },

    async createNewCustomField({ state }, { inputType }) {
      inputType = inputType === 'input' ? 'text' : inputType;

      const { type, value } = state.templateSaveData;
      const uniqueId = nanoid(9);
      const sameInputTypes = state.template.inputs.filter((element) => element.type === inputType);

      const fieldInfo = {
        name: `${_i18n().t('new')} ${_i18n().t(inputType).toLowerCase()} ${
          sameInputTypes.length + 1
        }`,
        type: inputType,
        customId: uniqueId,
      };

      const templateQuery = gql`mutation{newMasterField(input: {name: "${fieldInfo.name}", type: "${fieldInfo.type}", customId: "${fieldInfo.customId}", template: "${value}"})}`;
      const variantQuery = gql`mutation{newField(input: {name: "${fieldInfo.name}", type: "${fieldInfo.type}", customId: "${fieldInfo.customId}"})}`;

      try {
        await apolloClient.mutate({
          mutation: type === 'base' ? templateQuery : variantQuery,
        });

        const input = addInputDefaultOptions([fieldInfo], state.selectedElement)[0];

        return input;
      } catch (e) {
        console.log(e);
      }
    },
    async chooseInputField({ state, dispatch }, { inputType }) {
      await dispatch('loadInputFields');
      const { inputs } = collectElementsAllPage(state);
      const existingInputIds = inputs.map((i) => i.customId);

      const isElementTypeMatching = (i) =>
        i.type && ((inputType === 'input' && inputTypes.includes(i.type)) || i.type === inputType);
      const isElementUnique = (i) => !existingInputIds.includes(i.customId);

      const matchingInputs = state.template.inputs.filter(
        (i) => i && isElementTypeMatching(i) && isElementUnique(i),
      );

      let chosenInput;

      if (matchingInputs.length) {
        chosenInput = matchingInputs[0];
      } else {
        chosenInput = await dispatch('createNewCustomField', { inputType });
      }

      return chosenInput;
    },
    async switchInputElement({ state }, { customField, element = null }) {
      const selectedElement = state.selectedElement || element;

      const rawElement = _clone(selectedElement);
      _set(rawElement, 'data.form.customSettings', customField);

      // `newElement` will be a re-initialized copy of `element` but
      // with the new `data.form.customSettings` value.
      const newElement = _createElement(
        state,
        selectedElement.type,
        rawElement,
        {}, // skip merging with elementDefaults[type]
      );

      if (customField) {
        newElement.data.epicPlaceholder = customField.name;
      }

      Object.keys(newElement).forEach((key) => {
        Vue.set(selectedElement, key, _clone(newElement[key]));
      });
    },
    async updateInfoMessage(_, transKey) {
      _emit(this._vm.$bus, 'setUserInfoMessage', transKey);
    },
    async updateStylesheet({ dispatch }, { uid, styles }) {
      const id = `style-${uid}`;
      let sheet = document.getElementById(id);

      if (!sheet) {
        sheet = await dispatch('addStylesheet', uid);
      }

      const newCss = document.createTextNode(styles);

      if (sheet.childNodes.length) {
        sheet.replaceChild(newCss, sheet.childNodes[0]);
      } else {
        sheet.appendChild(newCss);
      }

      return sheet;
    },
    addStylesheet(_, uid) {
      const id = `style-${uid}`;
      let sheet = document.getElementById(id);
      let customCss = document.getElementById('om_custom_css');

      if (!customCss) {
        reinitStyles();
        customCss = document.getElementById('om_custom_css');
      }

      if (!sheet) {
        sheet = document.createElement('style');
        sheet.id = id;
        sheet.className = 'generated-element-style';
      }

      customCss.parentElement.insertBefore(sheet, customCss);

      return sheet;
    },
    updateElementStyle({ state, dispatch }, uid) {
      const styles = CssGenerator.element(uid, state.template);
      dispatch('updateStylesheet', { uid, styles });
    },
    removeStylesheet(_, uid) {
      const sheet = document.getElementById(`style-${uid}`);
      if (sheet) sheet.remove();
    },
    pushIntoElements({ state, dispatch }, payload) {
      const { uid, before, element } = payload;

      const index = state.template.elements.findIndex((element) => element.uid === uid);

      if (index > -1) {
        const elementIndex = before ? index : index + 1;
        state.template.elements.splice(elementIndex, 0, element);

        dispatch('addStylesheet', `style-${element.uid}`);
      }
    },
    removeFromElements({ state, dispatch }, uid) {
      const index = state.template.elements.findIndex((e) => e.uid === uid);

      if (index > -1) {
        const deleted = state.template.elements.splice(index, 1);

        dispatch('removeStylesheet', deleted[0].uid);
      }
    },
    moveInElements({ state }, payload) {
      const { targetId, before, sourceId } = payload;

      if (targetId && sourceId) {
        const sourceIndex = state.template.elements.findIndex((e) => e.uid === sourceId);
        const element = state.template.elements.splice(sourceIndex, 1);

        const index = state.template.elements.findIndex((e) => e.uid === targetId);
        const targetIndex = before ? index : index + 1;
        state.template.elements.splice(targetIndex, 0, element[0]);
      }
    },
    async copyInElements({ state, dispatch }, uid) {
      if (uid) {
        const index = state.template.elements.findIndex((e) => e.uid === uid);

        if (index > -1) {
          const wasWysiwygSelected = WYSIWYG_TYPES.includes(state.selectedElement?.type);
          const element = state.template.elements[index];
          let newElement;

          if (inputElements.includes(element.type)) {
            const type = element.type.toLowerCase().slice(2);
            const customField = await dispatch('chooseInputField', { inputType: type });

            const rawElement = {
              pageId: element.pageId,
              rowId: element.rowId,
              colId: element.colId,
              data: {},
            };

            if (customField) {
              rawElement.data.epicPlaceholder = customField.name;
              rawElement.data.form = {
                customSettings: customField,
              };
            }

            newElement = _createElement(state, element.type, rawElement);

            newElement.desktop = _clone(element.desktop);
            newElement.mobile = _clone(element.mobile);
          } else {
            newElement = _clone(state.template.elements[index]);
            newElement.uid = generateEleId();
          }

          state.template.elements.splice(index + 1, 0, newElement);
          dispatch('updateElementStyle', newElement.uid);

          if (wasWysiwygSelected) {
            if (WYSIWYG_TYPES.includes(newElement?.type)) {
              this._vm.$nextTick(() => {
                _selectElementByUid(state, newElement.uid);
              });
            } else {
              _deselectAll(state);
            }
          }
        }
      }
    },
    saveAutomaticCouponConfig(_, payload) {
      window.parent.om.bus.$emit('saveAutomaticCouponConfig', payload);
    },
    removeAutomaticCouponConfig(_, payload) {
      window.parent.om.bus.$emit('removeAutomaticCouponConfig', payload);
    },
    addRow({ state, dispatch }, payload) {
      const { uid, before, pageId = state.selectedPage.uid, count } = payload;

      let dropIndex = before ? 0 : state.template.elements.length - 1;
      if (uid) {
        dropIndex = state.template.elements.findIndex((el) => el.uid === uid);
        if (before === false) {
          dropIndex += 1;
        }
      }

      const row = emptyRow(count);
      row.pageId = pageId;

      const columns = [];
      for (let i = 0; i < count; i++) {
        const column = emptyColumn();
        column.pageId = row.pageId;
        column.rowId = row.uid;

        columns.push(column);
      }

      state.template.elements.splice(dropIndex, 0, row, ...columns);
      _selectElement(state, { uid: row.uid });
      [row, ...columns].forEach((element) => dispatch('updateElementStyle', element.uid));

      track('addEditorElement', { type: 'Row' });

      return { row, columns };
    },
    async generateContentWithAi({ dispatch }, { prompt }) {
      let response;
      try {
        const rawResponse = await apolloClient.query({
          query: GENERATE_CONTENT_WITH_AI,
          variables: { prompt },
        });
        response = rawResponse.data.generateContent;
        console.log('@@@@@response', response);
        // TODO[fopi]
        await dispatch('generateContentByJSON', { content: response });
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    async generateContentByJSON({ state, dispatch }, { content }) {
      try {
        const parsedText = JSON.parse(content);

        const template = state.template;
        const builder = new TemplateContentBuilder(template);
        template.elements = builder.cleanupElements(template.elements);
        const commands = builder.getCommands(parsedText);
        console.log('@@@@@commands', commands);
        // TODO[fopi]
        const customThemeStyles = await dispatch('customTheme/fetchAllThemeStyles');
        const commandBuilder = new CommandBuilder(commands, customThemeStyles);
        const elements = commandBuilder.buildElements();
        dispatch('directAddElements', { elements, styles: customThemeStyles });
      } catch (e) {
        console.error('generate from content');
        console.error(e);
      }
    },
    async getDropIdx({ state, dispatch }) {
      const { uid, isTop } = state.dropLocation;
      const targetIdx = state.template.elements.findIndex((el) => el.uid === uid);
      const target = state.template.elements[targetIdx];
      let dropIdx = state.template.elements.length;

      let rowId;
      let colId;

      if (target.type === 'OmPage') {
        const rows = state.template.elements.filter(
          ({ pageId, type }) => type === 'OmRow' && pageId === target.uid,
        );
        let rowTarget;
        if (rows.length > 0) {
          if (!isTop) {
            rowTarget = rows[rows.length - 1].uid;
          } else {
            rowTarget = rows[0].uid;
          }
        }
        const { row, columns } = await dispatch('addRow', {
          uid: rowTarget,
          before: isTop,
          count: 1,
        });
        const col = columns[0];

        rowId = row.uid;
        colId = col.uid;
      } else if (target.type === 'OmRow') {
        rowId = target.uid;
      } else if (target.type === 'OmCol') {
        rowId = target.rowId;
        colId = target.uid;
      } else {
        // dropped on element
        rowId = target.rowId;
        colId = target.colId;

        if (rowId === undefined) {
          const row = emptyRow(1);
          state.template.elements.push(row);
          rowId = row.uid;
        }

        if (_isNano(state)) {
          dropIdx = state.dropLocation.position === 'left' ? targetIdx : targetIdx + 1;
        } else {
          dropIdx = isTop ? targetIdx : targetIdx + 1;
        }
      }

      return { dropIdx, rowId, colId };
    },

    directAddElements({ state, dispatch }, { elements, styles }) {
      elements.forEach((element) => {
        const rawElement = {
          pageId: element.pageId,
          data: {},
        };
        const createdElement = _createElement(state, element.type, rawElement, null);
        const elementBuilder = new ElementBuilder(element, createdElement, styles);
        const builtElement = elementBuilder.buildElement();
        state.template.elements.push(builtElement);

        dispatch('updateElementStyle', builtElement.uid);
      });
    },

    async addElement({ state, dispatch, commit }, { customField, floatingImage } = {}) {
      if (!state.dragInfo) return;

      if (state.dragInfo.data.move) {
        return dispatch('moveElement');
      }

      const type = `Om${state.dragInfo.data}`;
      track('addEditorElement', { type });
      if (['OmProduct'].includes(type)) {
        trackIntercom(`${state.dragInfo.data} element added`);
      }
      const pageId = state.selectedPage.uid;
      // const pageId = target.type === 'OmPage' ? target.uid : target.pageId
      const rawElement = {
        pageId,
        data: {},
      };
      if (customField) {
        rawElement.data.epicPlaceholder = customField.name;
        rawElement.data.form = {
          customSettings: customField,
        };
      }

      const element = _createElement(state, type, rawElement, null);
      state.lastInsertedElementId = element.uid;

      if (state.dropLocation) {
        const { dropIdx, rowId, colId } = await dispatch('getDropIdx');

        element.rowId = rowId;
        element.colId = colId;

        state.template.elements.splice(dropIdx, 0, element); // push to new position
        state.dropLocation = null;
      } else if (element.type === ELEMENTS.OmFloatingImage) {
        element.desktop.position = floatingImage;
        state.template.elements.push(element);
      } else {
        // handle element click
        const rowElement = state.template.elements.find(
          (element) => element.pageId === state.selectedPage.uid && element.type === ELEMENTS.OmRow,
        );
        const colElement = state.template.elements.find(
          (element) => element.pageId === state.selectedPage.uid && element.type === ELEMENTS.OmCol,
        );
        if (!rowElement) {
          const { row, columns } = await dispatch('addRow', {
            uid: null,
            before: true,
            count: 1,
          });
          element.rowId = row.uid;
          element.colId = columns[0].uid;
        } else {
          element.rowId = rowElement.uid;
          element.colId = colElement.uid;
        }
        state.template.elements.push(element);
      }

      dispatch('setSpecialElementsOnPage');

      _selectElement(state, { uid: element.uid });

      if (element.type === 'OmCustomHTML') {
        _emit(this._vm.$bus, 'changeFormManagerVisibility', { show: 'customHTMLSettings' });
      }

      if (element.type === 'OmImage' || element.type === 'OmFloatingImage') {
        _emit(this._vm.$bus, 'setImageManagerDestination', {
          targetAttr: 'selectedElement',
          addElement: true,
        });
        _emit(this._vm.$bus, 'setSelectedPath', 'desktop');
        _emit(this._vm.$bus, 'showImageManager');
      }

      dispatch('updateElementStyle', element.uid);
      commit('customTheme/modifyElementByUid', {
        uid: element.uid,
        value: false,
      });

      this._vm.$nextTick(() => {
        window.requestAnimationFrame(() => {
          _emit(this._vm.$bus, 'repositionWysiwyg');
        });
      });
    },
    async moveElement({ state, dispatch }) {
      const { sourceElement: element } = state.dragInfo.data;
      let { dropIdx, rowId, colId } = await dispatch('getDropIdx');

      const sourceIdx = state.template.elements.findIndex((el) => el.uid === element.uid);

      element.rowId = rowId;
      element.colId = colId;

      if (sourceIdx < dropIdx) {
        // element will be removed before added to new position: need to shift drop index
        dropIdx -= 1;
      }

      state.template.elements.splice(sourceIdx, 1); // remove from old position
      state.template.elements.splice(dropIdx, 0, element); // push to new position

      if (element.type === 'OmVideo' || element.type === 'OmImage') {
        recalculateElementSize(state, element);
      }
    },
    copyPage(
      { state, dispatch, getters },
      { uid, copyElements = true, autoselect = true, overrides = {}, defaults = {} },
    ) {
      const sourcePageIdx = state.template.elements.findIndex(
        (element) => element.uid === uid && element.type === 'OmPage',
      );
      let sourcePage = state.template.elements[sourcePageIdx];
      if (sourcePage.isTeaser) {
        sourcePage = state.template.elements.find((el) => el.type === 'OmPage' && !el.isTeaser);
      }
      let newPage = _clone(sourcePage);
      const newElements = [];

      newPage.uid = generatePageId();
      newPage.data.title = getPageName(getters.pages.length + 1);

      if (copyElements) {
        const idMap = {};
        // OMC-168 exclude form inputs to prevent duplicate fields per campaign
        const noCopyElements = [
          'OmInput',
          'OmRadio',
          'OmCheckbox',
          'OmDropdown',
          'OmFeedback',
          'OmTextarea',
          'OmSurvey',
        ];
        const descendants = state.template.elements
          .filter((ele) => ele.pageId === uid)
          .filter((ele) => !noCopyElements.includes(ele.type));
        const newDescendants = _clone(descendants);

        newDescendants.forEach((newElement) => {
          const { type, uid: oldId } = newElement;

          newElement.uid = generateId(type);
          newElement.pageId = newPage.uid;

          if (type === 'OmRow' || type === 'OmCol') {
            idMap[oldId] = newElement.uid;
          }
        });

        newDescendants.forEach((newElement) => {
          if ('rowId' in newElement) {
            newElement.rowId = idMap[newElement.rowId];
          }

          if ('colId' in newElement) {
            newElement.colId = idMap[newElement.colId];
          }
        });

        newElements.push(...newDescendants);
      } else {
        const row = emptyRow(1);
        row.pageId = newPage.uid;
        const col = emptyColumn();
        col.pageId = newPage.uid;
        col.rowId = row.uid;
        newElements.push(row);
        newElements.push(col);
      }

      newPage = initializedServices.initWithDefaults(newPage, defaults);

      merge(newPage, overrides);

      state.template.elements.splice(sourcePageIdx + 1, 0, newPage, ...newElements);
      [newPage, ...newElements].forEach(({ uid }) => dispatch('updateElementStyle', uid));
      _emit(this._vm.$bus, 'setPages', getters.pages);

      if (autoselect) {
        _selectPage(state, newPage.uid);
      }

      return { page: newPage, elements: newElements };
    },
    addPage({ dispatch, getters }) {
      const pages = getters.pages;

      return dispatch('copyPage', { uid: pages[pages.length - 1].uid, copyElements: false });
    },
    removePage({ state, dispatch }, page) {
      const pageIdx = state.template.elements.findIndex(({ uid }) => page.uid === uid);

      if (page.uid === state.selectedPage.uid) {
        const pages = state.template.elements.filter((e) => isPage(e));
        const pageIdx = pages.findIndex(({ uid }) => page.uid === uid);
        let newSelectedPage;

        if (pageIdx > 0) {
          newSelectedPage = pages[pageIdx - 1];
        } else {
          newSelectedPage = pages[1];
        }

        _selectPage(state, newSelectedPage.uid);
      }

      Vue.delete(state.template.elements, pageIdx);
      dispatch('removeDescendants', page).then(() => {
        _emit(this._vm.$bus, 'pageRemoved', page.uid);
      });

      const coupons = state.template.elements.filter(
        (el) => el.type === 'OmCoupon' && el.pageId === page.uid,
      );
      if (coupons) {
        dispatch('removeAutomaticCouponConfig', { elements: coupons });
      }

      state.quillToolbarInfo.show = false;
    },
    movePage({ state, getters }, { from, to }) {
      state.quillToolbarInfo.show = false;
      const pages = getters.pages;
      const page = pages[from];
      const pageAtNewLocation = pages[to];

      const fromIdx = state.template.elements.findIndex((ele) => ele.uid === page.uid);
      let toIdx = from < to ? state.template.elements.length : 0;
      if (pageAtNewLocation) {
        toIdx = state.template.elements.findIndex((ele) => ele.uid === pageAtNewLocation.uid);
      }

      Vue.delete(state.template.elements, fromIdx);
      state.template.elements.splice(toIdx, 0, page);

      _emit(this._vm.$bus, 'setPages', getters.pages);
    },
    async removeRow({ state, dispatch }, uid) {
      const rowIdx = state.template.elements.findIndex(
        (el) => el.uid === uid && el.type === 'OmRow',
      );
      if (rowIdx !== -1) {
        const row = state.template.elements[rowIdx];
        if (state.selectedRow && state.selectedRow.uid === uid) {
          // selectedElement is in this row or the row itself is the currently selected element.
          _deselectAll();
        }
        _setEditorAttr(this._vm.$bus, 'paneLevel', 1);
        Vue.delete(state.template.elements, rowIdx);
        await dispatch('removeDescendants', row);

        dispatch('setSpecialElementsOnPage');
      } else {
        console.error(`Could not remove row since it is missing from elements[], uid = ${uid}`);
      }
    },
    async removeCol({ state, dispatch }, uid) {
      const colIdx = state.template.elements.findIndex(
        (el) => el.uid === uid && el.type === 'OmCol',
      );
      if (colIdx !== -1) {
        const column = state.template.elements[colIdx];
        if (state.selectedColumn && state.selectedColumn.uid === uid) {
          // selectedElement is in this column or the column itself is the currently selected element.
          _deselectAll();
        }

        Vue.delete(state.template.elements, colIdx);
        await dispatch('removeDescendants', column);

        dispatch('setSpecialElementsOnPage');
      } else {
        console.error(`Could not remove column since it is missing from elements[], uid = ${uid}`);
      }
    },
    async removeDescendants({ state, dispatch }, parent) {
      if (parent && parent.uid) {
        // Delete all descendants in reverse order (to avoid wrong element deletion because of array re-indexing)
        for (let idx = state.template.elements.length - 1; idx >= 0; idx--) {
          const element = state.template.elements[idx];

          if (
            (parent.type === 'OmPage' && element.pageId === parent.uid) ||
            (parent.type === 'OmRow' && element.rowId === parent.uid) ||
            (parent.type === 'OmCol' && element.colId === parent.uid)
          ) {
            await dispatch('removeElementByIdx', idx);
          }
        }
      }
    },
    removeElementByIdx({ state, dispatch, commit }, elementIdx) {
      const element = state.template.elements[elementIdx];
      if (element) {
        const selectedElementUid = state.selectedElement?.uid;
        _deselectAll(state);
        Vue.delete(state.template.elements, elementIdx);

        if (element.type === ELEMENTS.OmRow) {
          // In case of row delete, remove all nested columns also
          const nestedColIds = state.template.elements
            .filter((e) => e.type === ELEMENTS.OmCol && e.rowId === element.uid)
            .map((col) => col.uid);

          for (let i = 0; i < nestedColIds.length; i++) {
            const index = state.template.elements.findIndex(
              (element) => element.uid === nestedColIds[i],
            );
            Vue.delete(state.template.elements, index);
          }
        }

        if (element.type === ELEMENTS.OmCoupon) {
          dispatch('clearCoupons', element);
          dispatch('resetRedeemButtonAction');
        }

        dispatch('removeStylesheet', `style-${element.uid}`);

        if (selectedElementUid) {
          commit('selectElementByUid', { uid: selectedElementUid });
        }
      } else {
        console.error(
          'Tried to remove element by wrong index: ',
          elementIdx,
          'elements:',
          state.template.elements,
        );
      }
    },
    async removeElement({ state, dispatch }, { uid, resetPane = true }) {
      const elementIsSelected = state.selectedElement && state.selectedElement.uid === uid;
      if (elementIsSelected && resetPane) {
        _resetEditorPane(this._vm.$bus, state);
      }
      const elementIdx = state.template.elements.findIndex((el) => el.uid === uid);
      const element = state.template.elements[elementIdx];

      dispatch('removeElementByIdx', elementIdx);

      dispatch('setSpecialElementsOnPage');
      dispatch('removeStylesheet', `style-${uid}`);

      if (element.type === 'OmCoupon') {
        dispatch('removeAutomaticCouponConfig', { elements: [element] });
      }
    },
    updateColumnCount({ state, dispatch }, { uid, count }) {
      const rowIdx = state.template.elements.findIndex((e) => e.uid === uid);

      if (rowIdx > -1) {
        const row = state.template.elements[rowIdx];
        const columns = state.template.elements.filter(
          (e) => e.type === 'OmCol' && e.rowId === row.uid,
        );
        const diff = parseInt(count, 10) - columns.length;

        if (diff > 0) {
          Array(diff)
            .fill()
            .forEach(() => {
              const column = emptyColumn();

              column.pageId = row.pageId;
              column.rowId = uid;

              state.template.elements.push(column);

              dispatch('updateElementStyle', column.uid);
            });
        } else {
          columns.slice(diff).forEach(({ uid }) => dispatch('removeCol', uid));
        }
        state.template.elements[rowIdx].data.columnSplit = columnSplit(columns.length + diff);
        recalculateImageSizesInRow(state, uid);
        _selectElement(state, { uid });
      } else {
        console.error(
          `Could not find row to add/remove columns to/from it. (uid: ${uid}, count: ${count})`,
        );
      }
    },
    async copyElement({ dispatch }, uid) {
      dispatch('copyInElements', uid);
    },
    getInputs({ state }) {
      const elements = collectElementsAllPage(state);

      return elements.inputs;
    },
    getElementPageNumber({ state }, uid) {
      return findPageIndex(state, uid);
    },
    isElementOnCurrentPage({ state }, id) {
      const el = state.template.elements.find(({ uid }) => id === uid);

      if (el) {
        return el.pageId === state.selectedPage.uid;
      }

      return false;
    },
    couponRedeemShopifyAvailable({ state }) {
      _couponRedeemShopifyAvailable(state);
    },
    selectPage({ state }, index) {
      _selectPage(state, index);
    },
    setImageSrcsOnFirstPage({ state }) {
      const imageIds = collectElementsAllPage(state).images;
      state.imageSrcsOnFirstPage = state.template.images
        .filter((image) => imageIds.includes(image._id))
        .map((image) => image.url);

      if (!state.imageSrcsOnFirstPage.length) {
        state.imagesLoaded = true;
        _emit(this._vm.$bus, 'showAdminLoader', false);
        return;
      }

      let imagesLoaded = 0;

      state.imageSrcsOnFirstPage.forEach((src) => {
        const image = new Image();

        const handler = () => {
          imagesLoaded++;
          if (imagesLoaded === state.imageSrcsOnFirstPage.length) {
            state.imagesLoaded = true;
            _emit(this._vm.$bus, 'showAdminLoader', false);
          }
        };

        image.addEventListener('load', handler);
        image.addEventListener('error', handler);

        image.setAttribute('src', src);
      });
    },
    resetRedeemButtonAction({ state }) {
      state.template.elements.forEach((el) => {
        if (el.type === 'OmButton' && el.data.action === 'couponRedeem') {
          el.data.action = 'closePopup';
        }
      });
    },
    setSpecialElementsOnPage({ state, commit, dispatch }) {
      dispatch('couponRedeemShopifyAvailable');
      commit('setHasFeedbackOnPage');
      commit('setCouponOnPage');
      _setMaxPageHeightIfNeeded(state);
    },
    postInitElements(context) {
      initializedServices.postInitElementForV1(context);
      _editorStore().dispatch('fetchCouponCounts');
    },
    async clearCoupons({ state, getters }, el) {
      await _apollo(state).mutate({
        mutation: CLEAR_COUPONS,
        variables: {
          campaignId: getters.campaignInnerId,
          elementId: el.uid,
        },
      });
    },
    setThemeStyle({ state }, { uid, version }) {
      const blueprint = state.selectedElement;
      if (!blueprint || !blueprint.uid || uid !== blueprint.uid) {
        return;
      }

      const value = {
        data: _clone(blueprint.data),
        desktop: _clone(blueprint.desktop),
        mobile: _clone(blueprint.mobile),
      };

      if (blueprint.subElements) {
        value.subElements = blueprint.subElements;
      }
      const userCreated = state?.templateSaveData?.type !== 'base';
      _saveThemeStyle(this.state, blueprint.type, version, userCreated, value);
    },
    setElementDefaults({ state, dispatch }, { uid, update, extras }) {
      const etalon = state.selectedElement;

      if (etalon && etalon.uid === uid && etalon.type) {
        const defaults = state.template.elementDefaults[etalon.type];
        if (!defaults) return;

        if (['OmText', 'OmButton'].includes(etalon.type)) {
          const format = getQuillFormat();
          if (format) {
            // this will trigger a history save too
            etalon.desktop.quillFormat = { ...format };
          }
        }

        const elementIndexes = state.template.elements
          .map((el, index) => (el.type === etalon.type ? index : null))
          .filter((index) => !!index);

        const noNeedToSaveProps = ['data', 'selected'];
        Object.keys(defaults)
          .filter((defaultKey) => !noNeedToSaveProps.includes(defaultKey))
          .forEach((defaultKey) => {
            if (etalon[defaultKey])
              state.template.elementDefaults[etalon.type][defaultKey] = _clone(etalon[defaultKey]);
            if (update) {
              elementIndexes.forEach((elementIndex) => {
                const { uid } = state.template.elements[elementIndex];
                state.template.elements[elementIndex][defaultKey] = _clone(etalon[defaultKey]);
                dispatch('updateElementStyle', uid);
              });
            }
          });

        if (extras) {
          extras.forEach((extra) => {
            _set(state.template.elementDefaults[etalon.type], extra, _clone(_get(etalon, extra)));
          });
        }
      }
    },
    regenerateElementStyles({ state, dispatch }) {
      CssGenerator.generateElementStyles(state.template, dispatch, state.docker);
    },
    regenerateBaseStyles({ state }) {
      replaceCss(state.template, false, state.docker);
    },
    updateStyleProperty({ state, dispatch }, { uid, property, value, device }) {
      const element = findBox(state, uid);

      if (element) {
        device = device || state.mobilePreview ? 'mobile' : 'desktop';
        setWith(element[device], property, value, (ns, key, target) => {
          Vue.set(target, key, ns);
        });
        // Vue.set(element[device], property, value)
        dispatch('updateElementStyle', uid);
      }
    },
    initV2Teaser({ state, dispatch, getters }) {
      const V2Teaser = state.template.elements.find((e) => e.isTeaser);

      if (V2Teaser) {
        _emit(this._vm.$bus, 'setTeaserPage', V2Teaser);
      } else {
        const pages = getters.pages;
        dispatch('copyPage', {
          uid: pages[pages.length - 1].uid,
          copyElements: false,
          autoselect: false,
          defaults: {
            isTeaser: true,
            data: {
              display: {
                before: true,
                after: true,
              },
              position: 'top-left',
              mobilePosition: 'top',
              attentionSeeker: {
                type: null,
                freq: 3,
              },
              vAlign: 'om-flex-center',
            },
          },
          overrides: {
            desktop: {
              width: 300,
              minHeight: 150,
              margin: {
                allSides: true,
                left: 20,
                right: 20,
                top: 20,
                bottom: 20,
              },
            },
          },
        });
      }
    },

    saveMostUsedFonts({ state }, template) {
      const { fonts: fontsArray = [] } = collectFonts(template, ['closeButton']);
      const userId = state.databaseId;
      const currentFonts = localStorage.mostUsedFonts ? JSON.parse(localStorage.mostUsedFonts) : {};
      if (!currentFonts[userId]) currentFonts[userId] = {};
      fontsArray.forEach((fontName) => {
        currentFonts[userId][fontName] = currentFonts[userId][fontName]
          ? currentFonts[userId][fontName] + 1
          : 1;
      });
      localStorage.mostUsedFonts = JSON.stringify(currentFonts);
    },

    handleLogoDelete({ state }, { imageName }) {
      logoHide(state.template.elements, imageName);
    },
  },
  mutations: {
    setPhoneFrameStyles(state, styles) {
      state.phoneFrameStyles = { ...state.phoneFrameStyles, ...styles };
    },
    setIsResizedMobileEditor(state, value) {
      state.isResizedMobileEditor = value;
    },
    updateGlobalStyle(_, payload) {
      _editorStore().commit('updateGlobalStyle', payload);
    },
    copyElementStyle(state, payload) {
      state.clipboardStyle = payload;
    },
    resetCopyElementStyle(state) {
      state.clipboardStyle = {};
    },
    resetEditorPane(state, paneLevel) {
      _resetEditorPane(this._vm.$bus, state, paneLevel);
    },
    addImages(state, payload) {
      state.template.images = _clone(payload);
    },
    addFields(state, payload) {
      state.template.inputs = _clone(payload);
      _setEditorAttr(this._vm.$bus, 'inputs', state.template.inputs);
    },
    addCustomField(state, payload) {
      if (!state.template.inputs) this._vm.$set(state.template, 'inputs', []);
      state.template.inputs.push(payload);
      _setEditorAttr(this._vm.$bus, 'inputs', state.template.inputs);
    },
    setStateAttr(state, payload) {
      const { attr, stateAttr, value } = payload;
      const ref = stateAttr ? _get(state, stateAttr) : state;
      this._vm.$set(ref, attr, value);
    },
    updatePageTitle(state, { pageId, title }) {
      const index = state.template.elements.findIndex((e) => e.uid === pageId);
      state.template.elements[index].data.title = title;
    },
    setMobilePreview(state, payload) {
      state.mobilePreview = payload;
    },
    deselectAll(state) {
      _deselectAll(state);
    },
    updateStyle(state, { uid, style }) {
      const element = findBox(state, uid);
      if (element) {
        const device = state.mobilePreview ? 'mobile' : 'desktop';
        element[device] = style;
      }
    },
    updateData(state, payload) {
      const { uid, data } = payload;
      if (uid === undefined) {
        state.template.data = data;
      } else {
        const element = findBox(state, uid);
        if (element) {
          element.data = data;
        }
      }
    },
    updateDataProperty(state, { uid, property, value }) {
      if (property === 'display.mobile') {
        track('showTeaserOnMobile', {
          setting: value ? 'on' : 'off',
        });
      }
      const element = findBox(state, uid);

      if (element) {
        // _set(element.data, property, value)
        // Vue.set(element.data, property, value)
        setWith(element.data, property, value, (ns, key, target) => {
          Vue.set(target, key, ns);
        });
      }
    },
    setDragInfo(state, payload) {
      state.dragInfo = payload;
    },
    setSelectedElement(state, { uid, target }) {
      _selectElement(state, { uid, target });
    },
    selectElementByUid(state, uid) {
      _selectElement(state, { uid });
    },
    resetAndSelectSelectedElement(state) {
      _resetEditorPane(this._vm.$bus, state);
    },
    setSelectedRow(state, uid) {
      _selectElement(state, { uid });
    },
    moveRow(state, payload) {
      const { source, target, before } = payload;
      const sourceIdx = state.template.elements.findIndex((ele) => ele.uid === source);
      let targetIdx = state.template.elements.findIndex((ele) => ele.uid === target);

      if (sourceIdx !== -1 && targetIdx !== -1) {
        if (before === false) {
          targetIdx += 1;
        }

        const [sourceElement] = state.template.elements.splice(sourceIdx, 1);
        state.template.elements.splice(targetIdx, 0, sourceElement);
      } else {
        console.warn('Missing sourceIdx or targetIdx while trying to move rows.', {
          sourceIdx,
          targetIdx,
          source,
          target,
        });
      }
    },
    updateTemplateStyle(state, payload) {
      this._vm.$set(state.template, 'style', payload);
    },
    updatePageStyle(state, payload) {
      this._vm.$set(_page(state), 'style', payload);
    },
    setFontStatus(state, payload) {
      const { key, status } = payload;
      this._vm.$set(state.fonts[key], 'status', status);
      _emit(this._vm.$bus, 'setFontStatus', payload);
    },
    setTeaserPreview(state, payload) {
      state.teaserPreview = payload;
      _setFloatingActionsPosition(state);
    },
    setHasFeedbackOnPage: (state) => {
      return _setHasFeedbackOnPage(state);
    },
    setCouponOnPage: (state) => {
      return _setCouponOnPage(state);
    },
    setTeaserText(state, payload) {
      this._vm.$set(state.template.style.data, 'tabText', payload);
      _emit(this._vm.$bus, 'setTeaserText', payload);
    },
    isShopifyActive(state, active) {
      state.isShopifyActive = active;
    },
    isShoprenterActive(state, active) {
      state.isShoprenterActive = active;
    },
    setFloatingActionsPosition(state, mode) {
      _setFloatingActionsPosition(state, mode);
    },
    setUsedFonts(state, payload) {
      state.usedFonts = payload;
    },
    setRealUsedFonts(state, payload) {
      state.realUsedFonts = payload;
    },
    setSelectedElementWidth(state, value) {
      state.selectedElement.desktop.width = value;
      if (this._vm.$bus) {
        _setEditorAttr(this._vm.$bus, 'selectedElement', state.selectedElement);
      }
    },
    addTemplateFeature(state, feature) {
      const isWhitelisted = Object.values(TEMPLATE_FEATURES).includes(feature);

      if (isWhitelisted) {
        if (!Array.isArray(state.template.features)) {
          state.template.features = [];
        }
        if (!state.template.features.includes(feature)) state.template.features.push(feature);
      } else if (!isWhitelisted) {
        console.warn(`[WORKSPACE] Add feature flag: '${feature}' is not whitelisted`);
      }
    },
    removeTemplateFeature(state, feature) {
      const isWhitelisted = Object.values(TEMPLATE_FEATURES).includes(feature);

      if (isWhitelisted) {
        if (!Array.isArray(state.template.features) || state.template.features.length === 1) {
          state.template.features = [];
        } else {
          const index = state.template.features.findIndex((f) => feature === f);

          if (index > -1) {
            state.template.features.splice(index, 1);
          }
        }
      } else if (!isWhitelisted) {
        console.warning(`[WORKSPACE] Remove feature flag '${feature}' is not whitelisted`);
      }
    },
    resetTemplateFeatures(state) {
      state.template.features = [];
    },
    setAccountFeatures(state, features) {
      state.accountFeatures = features;
    },
    setTeaserVisibility(state, value) {
      const teaser = state.template.elements.find((v) => v.type === 'OmPage' && v.isTeaser);
      teaser.data.display.before = value;
      teaser.data.display.after = value;
      teaser.data.display.permanent = value;
    },
    setHoverbarActive(state, value) {
      state.hoverbarActive = value;
    },
    initFullscreen(state, isFullscreen) {
      if (isFullscreen) {
        state.template.style.overlay.backup = _clone(state.template.style.overlay);
        state.template.style.overlay.background.type = 'transparent';
        state.template.elements.forEach((element) => {
          if (element.type === 'OmPage' && !element.isTeaser) {
            element.desktop.width = null;
            element.mobile.width = 100;
          }
          if (element.type === 'OmCol') {
            element.desktop.verticalAlign = 'flex-start';
          }
          window.parent.om.bus.$emit('updateElementStyle', element.uid);
        });
      } else {
        state.template.elements.forEach((element) => {
          if (element.type === 'OmPage' && !element.isTeaser) {
            element.mobile.width = 82;
          }
          window.parent.om.bus.$emit('updateElementStyle', element.uid);
        });
        if (state.template.style.overlay.backup) {
          state.template.style.overlay = _clone(state.template.style.overlay.backup);
          delete state.template.style.overlay.backup;
        }
      }
    },
  },
});
