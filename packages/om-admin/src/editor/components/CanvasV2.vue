<template lang="pug">
mixin dropzone-right-bottom
  template(v-if="editMode")
    dropzone-v2(
      v-if="isNano"
      :position="'right'"
      :page="pageObject"
      :row="row"
      :column="column"
      :element="element"
      :disabled="(dragInfo && dragInfo.type === 'row') || (dragInfo && dragInfo.data.sourceElement === element)"
      @drop="drop"
    )
    dropzone-v2(
      v-else
      :position="'bottom'"
      :page="pageObject"
      :row="row"
      :column="column"
      :element="element"
      @drop="drop"
    )

mixin column-content
  template(v-if="!isVisualizedMarginsEnabled || docker || isNano")
    container.om-element(
      v-for="(element, elementIndex) in elementsOfColumn(column.uid)"
      type="element"
      :item="element"
      :index="elementIndex"
      :key="element.uid"
      :page="pageObject"
      :row="row"
      :column="column"
      :class="extraElementClass(element)"
      :draggable="isElementDraggable(element)"
      :data-track-property="getElementDataTrackProperty(element)"
      @mousedown.native="hoverbarMousedown(element)"
      @mouseup.native="hoverbarMouseup"
      @dragstart.native="dragstart($event, 'element', element, column, row)"
      @drag.native="drag"
      @mouseover.stop.native="mouseOverElement(row)"
      @mouseenter.native="mouseEnterElementOnCanvas(element)"
      @mouseleave.native="mouseLeaveElement(row), mouseLeaveElementOnCanvas(element)"
    )
      template(v-if="isNano")
        dropzone-v2(
          v-if="editMode"
          :position="'left'"
          :page="pageObject"
          :row="row"
          :column="column"
          :element="element"
          :disabled="(dragInfo && dragInfo.type === 'row') || (dragInfo && dragInfo.data.sourceElement === element)"
          :lastElement="isTheLastElement(elementIndex, elementsOfColumn(column.uid).length)"
          @drop="drop"
        )
      template(v-else)
        dropzone-v2(
          v-if="editMode"
          :position="'top'"
          :page="pageObject"
          :row="row"
          :column="column"
          :element="element"
          :disabled="(dragInfo && dragInfo.type === 'row') || (dragInfo && dragInfo.data.sourceElement === element)"
          :lastElement="isTheLastElement(elementIndex, elementsOfColumn(column.uid).length)"
          @drop="drop"
        )
      .backyard-near-element-hoverbar(
        :class="{ 'hoverbar-active': hoverbarActive }"
        v-if="isHeightResizerEnabled"
        @mouseenter="blockResizer(true)"
        @mouseleave="blockResizer(false)"
      )
      .element-hoverbar(
        v-if="!dragStarted && editMode && !isResizerInteraction"
        :class="hoverBarClasses(element)"
        @mouseenter="blockResizer(true)"
        @mouseleave="blockResizer(false)"
        :style="{ padding: readOnlyElement(element) ? '0' : null }"
      )
        .paint-tool-icon(
          v-if="isCopyableElements(element.type)"
          :class="{ disabled: isElementUnderCopy(element) || hasSmartTag(element) }"
          :data-track-property="`setting:copy-style|change:copy:${element.type}`"
          @click.stop="copyElementStyle(element)"
        )
          om-tooltip(transition="fade" big width="500px" theme="dark")
            .tooltip-title(style="line-height: initial") {{ $t('copyStyle.tooltip') }}
            template(slot="trigger")
              UilPaintTool(slot="icon" fill="white")
        .copy-icon
          i.fa.fa-copy(
            @click.stop="copyElement(element.uid)"
            v-if="!readOnlyElement(element) && !isFeedbackOrSurvey(element)"
          )
        .trash-icon
          i.fa.fa-trash-o(
            @mousedown.prevent=""
            @click.stop="removeElementFromCanvas(element)"
            v-if="!readOnlyElement(element)"
          )
      .element-hoverbar-dashed(
        v-if="editMode && !docker && !isResizerInteraction"
        :class="{ selected: selectedElement && selectedElement.uid === element.uid }"
      )
      template(v-if="element.type === 'OmProduct'")
        .om-swiper-button-prev(:data-item-id="element.uid")
        .om-swiper-button-next(:data-item-id="element.uid")
      component(:is="element.type" :item="element" :dragStarted="dragStarted")

      //- Handling when less then two elements are in the column
      template(v-if="elementsOfColumn(column.uid).length <= 2")
        template(v-if="needToGenerateBottom(column.uid, elementIndex)")
          +dropzone-right-bottom
      //- Handling when more than two elements are in the column
      template(v-else-if="elementsOfColumn(column.uid).length > 2")
        //- In case of the first element
        template(v-if="elementIndex === 0 && needToGenerateBottom(column.uid, elementIndex)")
          +dropzone-right-bottom
        //- In case of the middle elements
        template(
          v-if="elementIndex !== 0 && !isTheLastElement(elementIndex, elementsOfColumn(column.uid).length) && needToGenerateDropzone(elementsOfColumn(column.uid).length).MIDDLE.bottom"
        )
          +dropzone-right-bottom
        //- In case of the last element
        template(
          v-if="isTheLastElement(elementIndex, elementsOfColumn(column.uid).length) && needToGenerateDropzone(elementsOfColumn(column.uid).length).LAST.bottom"
        )
          +dropzone-right-bottom
  template(v-else)
    .editor-element-container-wrapper(
      v-for="(element, elementIndex) in elementsOfColumn(column.uid)"
      :class="{ nanobar: isNano }"
      :data-wrapper="element.uid"
    )
      .om-divider-helper(
        v-if="element.type === 'OmDivider'"
        @click.stop="onDividerHelperClick(element, $event)"
        :data-track-property="getElementDataTrackProperty(element)"
        :draggable="isElementDraggable(element)"
        @mousedown.stop="hoverbarMousedown(element)"
        @mouseup.stop="hoverbarMouseup"
        @dragstart.stop="dragstart($event, 'element', element, column, row)"
        @drag.stop="drag"
        @mouseover.stop="mouseOverElement(row)"
        @mouseenter.stop="mouseEnterElementOnCanvas(element), mouseEnterDividerHelper(element)"
        @mouseleave.stop="mouseLeaveElement(row), mouseLeaveElementOnCanvas(element), mouseLeaveDividerHelper(element)"
      )
      container.om-element(
        type="element"
        :item="element"
        :index="elementIndex"
        :key="element.uid"
        :page="pageObject"
        :row="row"
        :column="column"
        :class="extraElementClass(element)"
        :draggable="isElementDraggable(element)"
        :data-track-property="getElementDataTrackProperty(element)"
        @mousedown.native="hoverbarMousedown(element)"
        @mouseup.native="hoverbarMouseup"
        @dragstart.native="dragstart($event, 'element', element, column, row)"
        @drag.native="drag"
        @mouseover.stop.native="mouseOverElement(row)"
        @mouseenter.native="mouseEnterElementOnCanvas(element)"
        @mouseleave.native="mouseLeaveElement(row), mouseLeaveElementOnCanvas(element)"
      )
        template(v-if="isNano")
          dropzone-v2(
            v-if="editMode"
            :position="'left'"
            :page="pageObject"
            :row="row"
            :column="column"
            :element="element"
            :disabled="(dragInfo && dragInfo.type === 'row') || (dragInfo && dragInfo.data.sourceElement === element)"
            :lastElement="isTheLastElement(elementIndex, elementsOfColumn(column.uid).length)"
            @drop="drop"
          )
        template(v-else)
          dropzone-v2(
            v-if="editMode"
            :position="'top'"
            :page="pageObject"
            :row="row"
            :column="column"
            :element="element"
            :disabled="(dragInfo && dragInfo.type === 'row') || (dragInfo && dragInfo.data.sourceElement === element)"
            :lastElement="isTheLastElement(elementIndex, elementsOfColumn(column.uid).length)"
            @drop="drop"
          )
        .backyard-near-element-hoverbar(
          :class="{ 'hoverbar-active': hoverbarActive }"
          v-if="isHeightResizerEnabled"
          @mouseenter="blockResizer(true)"
          @mouseleave="blockResizer(false)"
        )
        .element-hoverbar(
          v-if="!dragStarted && editMode && !isResizerInteraction && !isElementResize"
          :class="hoverBarClasses(element)"
          @mouseenter="blockResizer(true)"
          @mouseleave="blockResizer(false)"
          :style="{ padding: readOnlyElement(element) ? '0' : null }"
        )
          .paint-tool-icon(
            v-if="isCopyableElements(element.type)"
            :class="{ disabled: isElementUnderCopy(element) || hasSmartTag(element) }"
            :data-track-property="`setting:copy-style|change:copy:${element.type}`"
            @click.stop="copyElementStyle(element)"
          )
            om-tooltip(transition="fade" big width="500px" theme="dark")
              .tooltip-title(style="line-height: initial") {{ $t('copyStyle.tooltip') }}
              template(slot="trigger")
                UilPaintTool(slot="icon" fill="white")
          .copy-icon
            i.fa.fa-copy(
              @click.stop="copyElement(element.uid)"
              v-if="!readOnlyElement(element) && !isFeedbackOrSurvey(element)"
            )
          .trash-icon
            i.fa.fa-trash-o(
              @mousedown.prevent=""
              @click.stop="removeElementFromCanvas(element)"
              v-if="!readOnlyElement(element)"
            )
        .element-hoverbar-dashed(
          v-if="editMode && !docker && !isResizerInteraction"
          :class="{ selected: selectedElement && selectedElement.uid === element.uid, 'element-resize-in-progress': element.uid === elementResizeUid }"
        )
        template(v-if="element.type === 'OmProduct'")
          .om-swiper-button-prev(:data-item-id="element.uid")
          .om-swiper-button-next(:data-item-id="element.uid")
        component(
          :is="element.type"
          ref="elementRefs"
          :item="element"
          :dragStarted="dragStarted"
          @element-resize="handleElementResize"
        )

        //- Handling when less then two elements are in the column
        template(v-if="elementsOfColumn(column.uid).length <= 2")
          template(v-if="needToGenerateBottom(column.uid, elementIndex)")
            +dropzone-right-bottom
        //- Handling when more than two elements are in the column
        template(v-else-if="elementsOfColumn(column.uid).length > 2")
          //- In case of the first element
          template(v-if="elementIndex === 0 && needToGenerateBottom(column.uid, elementIndex)")
            +dropzone-right-bottom
          //- In case of the middle elements
          template(
            v-if="elementIndex !== 0 && !isTheLastElement(elementIndex, elementsOfColumn(column.uid).length) && needToGenerateDropzone(elementsOfColumn(column.uid).length).MIDDLE.bottom"
          )
            +dropzone-right-bottom
          //- In case of the last element
          template(
            v-if="isTheLastElement(elementIndex, elementsOfColumn(column.uid).length) && needToGenerateDropzone(elementsOfColumn(column.uid).length).LAST.bottom"
          )
            +dropzone-right-bottom
      MarginVisualizer(
        v-if="elementIndex < (elementsOfColumn(column.uid).length - 1 || 0) && !isPaddingActive"
        :key="`${elementIndex}-margin-visualizer`"
        :uid="element.uid"
        :column="column.uid"
        :active="isMarginActive"
        :vertical="isNano"
        :scaledPreview="scaledPreview"
        @interact="toggleMarginActive"
        @hover="marginHover"
        @leave="marginLeave"
      )

container.om-canvas(
  :type="pageObject.isTeaser ? 'teaser' : 'canvas'"
  :key="pageObject.uid"
  :item="pageObject"
  :index="pageIndex"
  :style="canvasStyles"
  :class="[pageObject.data && pageObject.data.vAlign ? pageObject.data.vAlign : '', editMode ? 'animated fadeIn' : '', !selectedPage || pageObject.uid !== selectedPage.uid || teaserPreview ? 'om-canvas-hidden' : '', hasElWheel(pageObject) ? 'om-wheel-canvas' : '', pageObject.isTeaser ? 'om-teaser-canvas is-permanent' : '', customClasses(pageObject), hidePageResizerClass(pageObject), isVisualizerActive ? 'margin-handler-active' : '']"
  ref="container"
  data-track-property="location:workspace|setting:{event}"
  @drop.native="floatingImageDrop"
  @dragover.native="(event) => event.preventDefault()"
)
  template(v-for="floatingImage of floatingImages")
    container.om-element(
      v-if="floatingImage.pageId === pageObject.uid"
      type="element"
      :id="`${floatingImage.uid}_wrapper`"
      :item="floatingImage"
      :index="0"
      :key="floatingImage.uid"
      :page="pageObject"
      :class="extraElementClass(floatingImage)"
      :data-track-property="getElementDataTrackProperty(floatingImage)"
      @mousedown.native="floatingImageMousedown($event, floatingImage)"
      @dragstart.native.prevent="() => false"
      @mouseenter.native="mouseEnterElementOnCanvas(floatingImage)"
      @mouseleave.native="mouseLeaveElementOnCanvas(floatingImage)"
    )
      .element-hoverbar(
        v-if="!dragStarted && editMode && !isResizerInteraction"
        :class="hoverBarClasses(floatingImage)"
      )
        i.fa.fa-copy(
          @click.stop="copyElement(floatingImage.uid)"
          v-if="!readOnlyElement(floatingImage)"
        )
        i.fa.fa-trash-o(
          @mousedown.prevent=""
          @click.stop="removeElementFromCanvas(floatingImage)"
          v-if="!readOnlyElement(floatingImage)"
        )
      .element-hoverbar-dashed(
        v-if="!dragStarted && editMode && !docker && !isResizerInteraction"
        :class="{ selected: selectedElement && selectedElement.uid === floatingImage.uid }"
      )
      component(:is="floatingImage.type" :item="floatingImage")
  component(
    :is="backgroundAnimationName"
    v-if="((selectedPage && pageObject.uid === selectedPage.uid) || pageObject.isTeaser) && inEditor && backgroundAnimation && backgroundAnimationPopup"
    :itemCount="backAnimItemCount"
  )
  ribbon(v-if="template.style.ribbon.show && !pageObject.isTeaser")
  .om-global-close-button.om-popup-close(
    v-if="!isEmbedded && !pageObject.isTeaser"
    data-track-property="component:close-button"
    @click.stop="navigateTo('CloseButtonPane')"
  )
    span.om-popup-close-x(
      :data-delay="closeDelay"
      @mouseenter="mouseEnterElementOnCanvas({ uid: 'close-button' })"
      @mouseleave="mouseLeaveElementOnCanvas({ uid: 'close-button' })"
    ) X
  .om-canvas-content(
    :id="`canvas-content-${pageObject.uid}`"
    :class="{ 'no-scrolling': addClass }"
  )
    template(v-for="(row, rowIndex) in rowsOfPage(pageObject.uid)")
      .grid-noGutter.col-12.hover-row(
        :class="{ 'om-wheel-row': hasWheel, selected: isRowSelected(row), notDragging: dragging, 'fc-h100': isFakeClientFullscreenHeightHackNeeded(row) }"
      )
        container.canv-row.grid-noGutter.col-12(
          type="row"
          :item="row"
          :index="rowIndex"
          :page="pageObject"
          :key="row.uid"
          :draggable="isRowDraggable(row)"
          :data-track-property="getElementDataTrackProperty(row)"
          @dragstart.native="dragstart($event, 'row', row)"
          @drag.native="drag($event)"
          @dragend.native="dragend($event)"
          @mouseover.native="mouseOverRow(row)"
          @mouseenter.native="mouseEnterElementOnCanvas(row)"
          @mouseleave.native="mouseLeaveRow(row), mouseLeaveElementOnCanvas(row)"
          :class="customClasses(row)"
        )
          om-row-height-resizer(
            v-if="!dragStarted && isHeightResizerEnabled"
            :show="rowNeedsResizer(row)"
            :structuralElement="row"
            :onlyDesktop="true"
            :isResizerBlockerInteraction="isResizerBlockerInteraction"
            :parentRefs="$refs"
          )
          dropzone-v2(
            v-if="editMode"
            position="top"
            :page="pageObject"
            :row="row"
            :disabled="!dragInfo || dragInfo.type !== 'row'"
            @drop="drop"
          )
          .edit-mode-tab.top(
            v-if="!dragStarted && editMode && !isResizerInteraction"
            v-show="row.uid !== hideRowId"
          ) {{ $t('editMode') }}
          .backyard-near-row-hoverbar(
            v-if="isHeightResizerEnabled"
            @mouseenter="blockResizer(true)"
            @mouseleave="blockResizer(false)"
          )
          .hoverbar(
            v-if="!dragStarted && editMode && !isResizerInteraction"
            v-show="row.uid !== hideRowId"
            @mouseenter="blockResizer(true)"
            @mouseleave="blockResizer(false)"
          )
            i.fa.fa-lg.fa-trash-o(v-if="!isNano" @click.stop="removeRowFromCanvas(row)")
          template(v-for="(column, columnIndex) in columnsOfRow(row.uid)")
            container.canv-col(
              type="column"
              :item="column"
              :index="columnIndex"
              :key="column.uid"
              :page="pageObject"
              :row="row"
              :columnCount="columnsOfRow(row.uid).length"
              :data-row="row.uid"
              :data-column="column.uid"
              :class="[columnSelected(column), contentAlignment(column, columnIndex), hasWheel ? 'has-wheel-col' : '', hasElWheel(column) ? 'om-wheel-canv-col' : '', isHeightResizerEnabled ? 'block-resize-helper' : '', customClasses(column)]"
              :data-track-property="getElementDataTrackProperty(column)"
              @mouseover.native="mouseEnterElementOnCanvas(column)"
              @mouseleave.native="mouseLeaveElementOnCanvas(column)"
            )
              om-col-height-resizer(
                v-if="isHeightResizerEnabled"
                :show="mouseOverColumnId === column.uid"
                :structuralElement="column"
                :onlyMobile="true"
                :isResizerBlockerInteraction="isResizerBlockerInteraction"
                :parentRefs="$refs"
              )
              dropzone-v2(
                v-if="editMode"
                :page="pageObject"
                :row="row"
                :column="column"
                :disabled="(dragInfo && dragInfo.type === 'row') || elementsOfColumn(column.uid).length > 0"
                @drop="drop"
              )
              .col-hoverbar(
                v-if="editMode && !isResizerInteraction"
                v-show="row.uid !== hideRowId"
              )
              .om-margin-backdrop(v-if="isVisualizerActive || isElementResize" @mouseup.stop)
              template(v-if="isNano || !isHeightResizerEnabled || hasWheel")
                +column-content
              template(v-else)
                .om-column-min-height-helper(:id="columnMinHeightHelperId(column.uid)")
                  +column-content

              om-col-resizer(
                v-if="editMode && !mobilePreview && columnIndex !== columnsOfRow(row.uid).length - 1"
                :show="mouseOverRowId === row.uid"
                :columnIndex="columnIndex"
                :row="row"
                :isResizerBlockerInteraction="isResizerBlockerInteraction"
              )
              PaddingVisualizer(
                v-if="editMode && (!isMarginActive || !isElementResize)"
                :uid="column.uid"
                :scaledPreview="scaledPreview"
                :active="isPaddingActive"
                :key="`padding-visualizer-${column.uid}`"
                @interact="togglePaddingActive"
              )
          transition(name="fade")
            .hoverbar-dashed(
              v-if="editMode && ((!isResizerInteraction && !isElementResize) || columnSplitRowId === row.uid)"
              :class="{ selected: isRowSelected(row) && columnSplitRowId !== row.uid }"
              :style="{ display: row.uid === hideRowId ? 'none' : '' }"
            )
          dropzone-v2(
            v-if="editMode"
            position="bottom"
            :page="pageObject"
            :row="row"
            :disabled="!dragInfo || dragInfo.type !== 'row'"
            @drop="drop"
          )
  .om-teaser-close(
    v-if="pageObject.isTeaser && isPermanentTeaser"
    data-track-property="component:close-button"
    @click.stop="navigateTo('CloseButtonPane')"
  )
    span.om-teaser-close-x(
      @mouseenter="mouseEnterElementOnCanvas({ uid: 'close-button' })"
      @mouseleave="mouseLeaveElementOnCanvas({ uid: 'close-button' })"
    ) X
  template(v-if="!isVisualizerActive || !isElementResize")
    om-page-resizer(
      v-if="!pageObject.isTeaser || (pageObject.isTeaser && !mobilePreview)"
      :isPageElementSelected="isPageElementSelected"
      :structuralElement="pageObject"
      :isResizerBlockerInteraction="isPageResizerBlocked"
    )
    dropzone-v2.fillgap(
      position="top"
      :page="pageObject"
      @drop="drop"
      :disabled="(isNano && hasRow) || (pageObject.isTeaser && hasRow) || (dragInfo && dragInfo.event !== 'click')"
    )
</template>

<script>
  import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
  import { get as _get } from 'lodash';
  import Snowing from '@/editor/components/animations/Snowing.vue';
  import OmPageResizer from '@/editor/components/resizer/Page';
  import OmColResizer from '@/editor/components/resizer/Column';
  import OmRowHeightResizer from '@/editor/components/resizer/RowHeight';
  import OmColHeightResizer from '@/editor/components/resizer/ColumnHeight';
  import DropzoneV2 from '@/editor/components/DropzoneV2.vue';
  import * as EditorElements from '@/editor/components/elements';
  import { UilPaintTool } from '@iconscout/vue-unicons';
  import backgroundAnimationMixin from '@/editor/mixins/backgroundAnimation';
  import floatingImageMixin from '@/editor/mixins/floatingImage';
  import teaserMixin from '@/editor/mixins/teaser';
  import dropzone from '@/editor/mixins/dropzone';
  import copyStyle from '@/editor/mixins/copyStyle';
  import OmTooltip from '@/components/Elements/Tooltip/Tooltip.vue';
  import MarginVisualizer from '@/editor/components/visualizers/MarginVisualizer.vue';
  import PaddingVisualizer from '@/editor/components/visualizers/PaddingVisualizer.vue';

  export default {
    components: {
      OmPageResizer,
      OmColResizer,
      OmRowHeightResizer,
      OmColHeightResizer,
      DropzoneV2,
      ...EditorElements,
      'animation-snowing': Snowing,
      UilPaintTool,
      OmTooltip,
      MarginVisualizer,
      PaddingVisualizer,
    },
    mixins: [backgroundAnimationMixin, floatingImageMixin, dropzone, copyStyle, teaserMixin],
    props: {
      pageObject: { type: Object },
    },
    data() {
      return {
        isResizerInteraction: false,
        isBlockedResizer: false,
        columnSplitRowId: null,
        addClass: false,
        isElementResize: false,
        elementResizeUid: null,
      };
    },
    computed: {
      ...mapState(['hoverbarActive', 'quillToolbarInfo', 'scaledPreview']),
      ...mapGetters(['isFullscreen', 'isFullHeightSidebar', 'isVisualizedMarginsEnabled']),
      canvasStyles() {
        if (!this.pageObject.isTeaser) return;

        return {
          filter: this.isTeaserEnabled ? 'unset' : 'grayscale(100%)',
          'pointer-events': this.isTeaserEnabled ? 'all' : 'none',
        };
      },
      isHeightResizerEnabled() {
        if (this.hasWheel) return false;
        if (this.mobilePreview && this.isFullscreen) return false;
        return true;
      },
      isPageElementSelected() {
        if (
          this.selectedElement?.uid &&
          this.selectedPage?.uid &&
          this.selectedElement.uid === this.selectedPage.uid
        ) {
          return true;
        }
        return false;
      },
      isResizerBlockerInteraction() {
        if (this.isResizerInteraction || this.isBlockedResizer) return true;
        return false;
      },
      isPageResizerBlocked() {
        return (
          this.isResizerBlockerInteraction || !!this.mouseOverRowId || !!this.mouseOverElementId
        );
      },
      isPermanentTeaser() {
        return this.template.data.isPermanentTeaser;
      },
    },
    created() {
      this.$bus?.$on?.('resizerInteraction', (status) => {
        this.isResizerInteraction = status;
      });
      this.$bus?.$on?.('columnSplitRowId', (id) => {
        this.columnSplitRowId = id;
      });
    },
    mounted() {
      if (this.isFullHeightSidebar) {
        this.checkHeight();
        const observer = new MutationObserver(() => {
          this.checkHeight();
        });
        const canvRow = document.querySelector(`#${this.pageObject.uid} .canv-row`);
        if (canvRow) {
          observer.observe(canvRow, {
            attributes: true,
            childList: true,
            subtree: true,
          });
        }

        window.addEventListener('resize', this.checkHeight);
        this.observer = observer;
      }
    },
    beforeDestroy() {
      if (this.isFullHeightSidebar) {
        if (this.observer) {
          this.observer.disconnect();
        }
        window.removeEventListener('resize', this.checkHeight);
      }
    },
    methods: {
      ...mapMutations(['setStateAttr', 'deselectAll', 'selectElementByUid']),
      ...mapActions(['selectElement']),
      handleElementResize({ isResizing, uid }) {
        this.isElementResize = isResizing;
        this.elementResizeUid = uid;
      },
      checkHeight() {
        this.$nextTick(() => {
          const container = document.querySelector(`#${this.pageObject.uid}`);
          const canvRow = document.querySelector(`#${this.pageObject.uid} .canv-row`);

          if (container && canvRow) {
            const containerHeight = container.clientHeight;
            const canvRowHeight = canvRow.clientHeight;
            this.addClass = canvRowHeight < containerHeight;
          }
        });
      },
      blockResizer(value) {
        this.isBlockedResizer = value;
      },
      columnMinHeightHelperId(uid) {
        return `om-column-min-height-helper-${uid}`;
      },
      removeElementFromCanvas(element) {
        this.mouseOverElementId = null;
        /* hack to hide tiptap, selectedElement not sync in the two store state */
        if (this.selectedElement) {
          this.selectElementByUid(this.selectedElement.pageId ?? this.selectedElement.uid);
          this.$nextTick(() => {
            this.deselectAll();
          });
        }
        /* en of hack */
        this.removeElement({ uid: element.uid });
        this.blockResizer(false);
      },
      removeRowFromCanvas(row) {
        this.mouseOverElementId = null;
        this.mouseOverColumnId = null;
        this.hideRowId = null;
        this.mouseOverRowId = null;
        this.removeRow(row.uid);
        this.blockResizer(false);
      },
      customClasses(element) {
        return _get(element, 'data.customClasses', '');
      },
      hidePageResizerClass(page) {
        if (!this.editMode || !this.mobilePreview) return '';
        const width = _get(page, 'mobile.width');
        return width && width > 89 ? 'no-resizer' : '';
      },
      rowNeedsResizer(row) {
        return row.desktop.heightType === 'manual' && this.mouseOverRowId === row.uid;
      },
      isFakeClientFullscreenHeightHackNeeded(row) {
        return !this.editMode && row.desktop.heightType === '100h';
      },
      onDividerHelperClick(element, e) {
        this.selectElement({ uid: element.uid, target: e.target });
      },
      mouseEnterDividerHelper(element) {
        this.changeHoverbarClass(element.uid, 'add');
      },
      mouseLeaveDividerHelper(element) {
        this.changeHoverbarClass(element.uid, 'remove');
      },
      changeHoverbarClass(uid, fn) {
        const hoverbar = document.querySelector(`[data-wrapper="${uid}"] .om-element`);
        hoverbar?.classList?.[fn]?.('show-hoverbar');
      },
      marginHover(element) {
        this.hideRowId = element?.closest?.('.canv-row')?.id ?? null;
      },
      marginLeave(element) {
        if (this.hideRowId === element?.closest?.('.canv-row')?.id) {
          this.hideRowId = null;
        }
      },
    },
  };
</script>
<style lang="sass">
  .editor-element-container-wrapper,
  .om-teaser-canvas
    position: relative

  .editor-element-container-wrapper
    width: 100%

  .om-divider-helper
    cursor: grab
    position: absolute
    top: -5px
    left: 0
    right: 0
    bottom: 0
    width: 100%
    height: 10px
    z-index: 4
  .om-margin-backdrop
    position: absolute
    top: 0
    left: 0
    right: 0
    bottom: 0
    width: 100%
    height: 100%
    z-index: 20
</style>
