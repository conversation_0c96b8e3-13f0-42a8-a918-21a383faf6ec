<template lang="pug">
.om-padding-visualizer-wrapper(
  :class="{ 'scaled-mobile-preview': scaledPreview && mobilePreview, active }"
)
  .om-padding-visualizer(
    v-for="key in LIST_PADDING_KEYS"
    :class="{ [`om-padding-visualizer-${key}`]: true }"
  )
    .om-padding-visualizer-trigger(
      :class="{ [key]: true, active: mousedownKey === key }"
      @mouseover.stop="mouseOver($event, key)"
      @mouseleave.stop="mouseLeave($event, key)"
      @mousedown.stop="pinched($event, key)"
      @mouseup.stop="released"
    )
    .om-padding-visualizer-handle(:class="{ [key]: true, active: mousedownKey }")
</template>

<script>
  import { get, debounce } from 'lodash-es';
  import { mapActions, mapState, mapMutations } from 'vuex';
  import { track } from '@/services/xray';

  const LIST_PADDING_KEYS = ['top', 'right', 'bottom', 'left'];
  const VERTICAL_KEYS = ['right', 'left'];

  export default {
    props: {
      uid: {
        type: String,
        required: true,
      },
      active: {
        type: String,
        default: null,
      },
      scaledPreview: {
        type: Boolean,
        default: false,
      },
    },
    data: () => ({
      LIST_PADDING_KEYS,
      mousedown: false,
      mousedownKey: null,
      startY: null,
      startX: null,
    }),
    computed: {
      ...mapState({
        elements: (state) => state.template.elements,
      }),
      ...mapState(['mobilePreview', 'selectedColumn']),
      element() {
        return this.elements.find(({ uid }) => uid === this.uid);
      },
      paddings() {
        const paddings = this.extractPaddingValues(this.element);

        if (!paddings) return {};

        return paddings;
      },
      device() {
        return this.mobilePreview ? 'mobile' : 'desktop';
      },
      inactive() {
        return this.active && this.uid !== this.active;
      },
    },
    watch: {
      paddings: {
        handler() {
          this.setPaddingVariables();
        },
        deep: true,
      },
      element: {
        handler() {
          this.$nextTick(() => {
            this.setPaddingVariables();
          });
        },
        deep: true,
      },
      mobilePreview() {
        this.setPaddingVariables();
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      ...mapActions(['regenerateElementStyles']),
      ...mapMutations('wysiwyg', ['SET_MARGIN_VISUALIZER_ACTIVE']),
      ...mapActions('wysiwyg', ['showWysiwyg', 'hideWysiwyg']),
      async pinched(event, key) {
        event.preventDefault();
        event.stopPropagation();
        if (this.active || event.which !== 1) return;

        this.SET_MARGIN_VISUALIZER_ACTIVE(true);
        this.hideWysiwyg();
        this.$emit('interact', this.uid);
        this.mousedown = true;
        this.mousedownKey = key;

        this.startY = event.screenY;
        this.startX = event.screenX;
        this.startPadding = this.paddings[key];
        const html = document.querySelector('html');
        html.addEventListener('mousemove', this.move, true);
        html.addEventListener('mouseup', this.released, true);
      },
      async released(event) {
        event.preventDefault();
        event.stopPropagation();
        this.$emit('interact', null);
        this.SET_MARGIN_VISUALIZER_ACTIVE(false);
        this.showWysiwyg();
        this.mousedown = false;
        this.mousedownKey = null;
        const html = document.querySelector('html');
        html.removeEventListener('mousemove', this.move, true);
        html.removeEventListener('mouseup', this.released, true);
      },
      extractPaddingValues(element) {
        if (!element || !element[this.device]?.padding) {
          return null;
        }

        return LIST_PADDING_KEYS.reduce((prev, key) => {
          if (this.mobilePreview) {
            const fallbackValue = get(element, `desktop.padding.${key}`, 0);
            prev[key] =
              get(element, `${this.device}.padding.${key}`, fallbackValue) ?? fallbackValue;
          } else {
            prev[key] = get(element, `${this.device}.padding.${key}`, 0);
          }

          return prev;
        }, {});
      },
      setPaddingVariables() {
        Object.entries(this.paddings).forEach(([key, value]) => {
          this.$el?.style?.setProperty(`--padding-${key}`, value);
          this.$el?.style?.setProperty(`--padding-em-${key}`, `${value / 16}em`);
        });
      },
      getDOMElement() {
        const marginId = this.uid.replace('ele_', 'ele_ma_');
        return document.querySelector(`[data-margin="${marginId}"]`);
      },
      init() {
        setTimeout(() => {
          this.setPaddingVariables();
        }, 500);
      },
      debounceTrack: debounce((properties) => {
        track('padding-visualizer-changed', properties);
      }, 250),
      move(event) {
        console.log('move', event);
        const key = this.mousedownKey;
        const vertical = VERTICAL_KEYS.includes(key);
        const startXY = vertical ? this.startY : this.startX;
        const currentXY = vertical ? event.screenY : event.screenX;

        let spacing = this.startPadding + (currentXY - startXY);
        console.log('pos', { startXY, currentXY }, this.startPadding, spacing);

        console.log('spacing', spacing);

        if (spacing < 0) {
          spacing = 0;
        }

        if (this.element[this.device].padding?.allSides) {
          this.element[this.device].padding.allSides = false;
        }

        this.element[this.device].padding[key] = spacing;

        this.regenerateElementStyles();

        this.debounceTrack({ spacing });

        this.setPaddingVariables();
      },
      leave() {
        this.$emit('interact', false);
        this.mousedown = false;
        this.mousedownKey = null;
        this.$el.removeEventListener('mousemove', this.move);
      },
      isElementHidden(element) {
        return get(element, `${this.device}.hidden`, false);
      },
      mouseOver() {
        this.$emit('hover', this.$el);
      },
      mouseLeave() {
        this.$emit('leave', this.$el);
      },
    },
  };
</script>

<style lang="sass">
  @import "@/sass/variables/_colors.sass"

  .om-padding-visualizer
    z-index: 11
    pointer-events: hover
    &:hover .om-padding-visualizer-handle
      opacity: 1
    &-wrapper
      position: absolute
      overflow: hidden
      top: 0
      left: 0
      width: 100%
      height: 100%
      z-index: 1
    &-left,
    &-top,
    &-right,
    &-bottom
      position: absolute
      top: 0
      bottom: 0
      left: 0
      right: 0
      height: 100%
      width: 100%
      z-index: 10
    &-left
      width: var(--padding-em-left)
      left: 0
      right: auto
      z-index: 9
    &-right
      width: var(--padding-em-right)
      left: auto
      right: 0
      z-index: 9
    &-top
      height: var(--padding-em-top)
      bottom: auto
      top: 0
    &-bottom
      height: var(--padding-em-bottom)
      top: auto
      bottom: 0

    &-handle
      margin-bottom: -1px
      opacity: 0
      height: 0
      background: $om-orange
      width: 16px
      pointer-events: none
      border: 2px solid $om-orange
      box-sizing: content-box !important
      margin: auto
      position: absolute
      top: calc(50% - 1px)
      left: calc(50% - 11px)
    &-trigger
      position: absolute
      margin: auto
      top: calc(50% - 11px)
      left: calc(50% - 18px)
      right: auto
      height: 22px
      width: 36px
      cursor: row-resize
      pointer-events: all
      &:hover,
      &.active
        & ~ .om-padding-visualizer-handle
          opacity: 1

    &-left,
    &-right
      .om-padding-visualizer-trigger
        margin: auto
        height: 36px
        width: 22px
        cursor: col-resize
        top: calc(50% - 22px)
        left: calc(50% - 11px)
      .om-padding-visualizer-handle
        height: 16px
        width: 0
        margin-top: auto
        margin-bottom: auto
        top: calc(50% - 8px)
        left: calc(50% - 2px)
</style>
