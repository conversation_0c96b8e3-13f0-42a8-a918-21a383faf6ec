<template lang="pug">
.om-margin-visualizer(
  :class="{ mousedown, vertical, inactive, 'scaled-mobile-preview': scaledPreview && mobilePreview }"
)
  .om-margin-visualizer-trigger(
    @mouseover.stop="mouseOver"
    @mouseleave.stop="mouseLeave"
    @mousedown.stop="pinched"
    @mouseup.stop="released"
    :class="{ mousedown, vertical, inactive, 'scaled-mobile-preview': scaledPreview && mobilePreview }"
  )
  .om-margin-visualizer-handle
</template>

<script>
  import { get, debounce } from 'lodash-es';
  import { mapActions, mapState, mapMutations } from 'vuex';
  import { track } from '@/services/xray';

  const LIST_MARGIN_KEYS = ['top', 'right', 'bottom', 'left'];

  const SMALL_SPACE_LIMIT = 8;

  export default {
    props: {
      uid: {
        type: String,
        required: true,
      },
      column: {
        type: String,
        required: true,
      },
      active: {
        type: String,
        default: null,
      },
      vertical: {
        type: Boolean,
        default: false,
      },
      scaledPreview: {
        type: Boolean,
        default: false,
      },
    },
    data: () => ({
      store: null,
      boundaries: null,
      mousedown: false,
      startY: null,
      startX: null,
      smallSpaceLimit: SMALL_SPACE_LIMIT,
    }),
    computed: {
      ...mapState({
        elements: (state) => state.template.elements,
      }),
      ...mapState(['mobilePreview', 'selectedElement']),
      columnElements() {
        return this.elements.filter(({ colId }) => colId === this.column);
      },
      hideMarginHandle() {
        const prevMargins = this.extractMarginValues(this.previousElement);
        if (!prevMargins) return true;

        if (this.uid === this.selectedElement?.uid) {
          return prevMargins.bottom < SMALL_SPACE_LIMIT;
        }

        const nextMargins = this.extractMarginValues(this.nextElement);
        if (!nextMargins) return true;

        if (this.nextElement?.uid && this.nextElement?.uid === this.selectedElement?.uid) {
          return nextMargins.top < SMALL_SPACE_LIMIT;
        }

        return this.isElementHidden(this.previousElement);
      },
      rawPrevIndex() {
        return this.elements.findIndex(({ uid }) => uid === this.uid);
      },
      rawNextIndex() {
        const next = this.nextElement?.uid;
        return this.elements.findIndex(({ uid }) => uid === next);
      },
      index() {
        return this.columnElements.findIndex(({ uid }) => uid === this.uid);
      },
      previousElement() {
        return this.columnElements.find(({ uid }) => uid === this.uid);
      },
      nextElement() {
        const prev = this.columnElements.findIndex(({ uid }) => this.uid === uid);
        let nextIndex = prev + 1;
        let nextElement;
        let possibleNextElement;

        do {
          possibleNextElement = this.columnElements[nextIndex];
          if (!this.isElementHidden(possibleNextElement)) {
            nextElement = possibleNextElement;
          }
          nextIndex++;
        } while (!nextElement && possibleNextElement);

        return nextElement;
      },
      margins() {
        const prevMargins = this.extractMarginValues(this.previousElement);
        const nextMargins = this.extractMarginValues(this.nextElement);

        if (!prevMargins || !nextMargins) return {};

        return {
          height: Math.max(prevMargins.bottom, nextMargins.top),
          width: Math.max(prevMargins.right, nextMargins.left),
        };
      },
      device() {
        return this.mobilePreview ? 'mobile' : 'desktop';
      },
      inactive() {
        return this.active && this.uid !== this.active;
      },
    },
    watch: {
      margins: {
        handler() {
          this.setMarginVariables();
        },
        deep: true,
      },
      selectedElement: {
        handler() {
          this.$nextTick(() => {
            this.setMarginVariables();
          });
        },
        deep: true,
      },
      mobilePreview() {
        this.setMarginVariables();
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      ...mapActions(['regenerateElementStyles']),
      ...mapMutations('wysiwyg', ['SET_MARGIN_VISUALIZER_ACTIVE']),
      ...mapActions('wysiwyg', ['showWysiwyg', 'hideWysiwyg']),
      async pinched(event) {
        event.preventDefault();
        event.stopPropagation();
        if (this.active || event.which !== 1) return;

        this.SET_MARGIN_VISUALIZER_ACTIVE(true);
        this.hideWysiwyg();
        this.$emit('interact', this.uid);
        this.mousedown = true;

        this.startY = event.pageY;
        this.startX = event.pageX;
        const html = document.querySelector('html');
        html.addEventListener('mousemove', this.move, true);
        html.addEventListener('mouseup', this.released, true);
      },
      async released(event) {
        event.preventDefault();
        event.stopPropagation();
        this.$emit('interact', null);
        this.SET_MARGIN_VISUALIZER_ACTIVE(false);
        this.showWysiwyg();
        this.mousedown = false;
        const html = document.querySelector('html');
        html.removeEventListener('mousemove', this.move, true);
        html.removeEventListener('mouseup', this.released, true);
      },
      extractMarginValues(element) {
        if (!element || !element[this.device]?.margin) {
          return null;
        }

        return LIST_MARGIN_KEYS.reduce((prev, key) => {
          if (this.mobilePreview) {
            const fallbackValue = get(element, `desktop.margin.${key}`, 0);
            prev[key] =
              get(element, `${this.device}.margin.${key}`, fallbackValue) ?? fallbackValue;
          } else {
            prev[key] = get(element, `${this.device}.margin.${key}`, 0);
          }

          return prev;
        }, {});
      },
      setMarginVariables() {
        Object.entries(this.margins).forEach(([key, value]) => {
          this.$el?.style?.setProperty(`--space-${key}`, value);
          this.$el?.style?.setProperty(`--space-em-${key}`, `${value / 16}em`);
        });
      },
      getDOMElement() {
        const marginId = this.uid.replace('ele_', 'ele_ma_');
        return document.querySelector(`[data-margin="${marginId}"]`);
      },
      init() {
        setTimeout(() => {
          this.setMarginVariables();
        }, 500);
      },
      debounceTrack: debounce(({ component1, component2, spacing }) => {
        track('margin-visualizer-changed', {
          component_top: component1,
          component_bottom: component2,
          spacing,
        });
      }, 250),
      move(event) {
        // movementY, movementX is Pointer Lock API feature but we got it while unlocked
        const delta = this.vertical ? event.movementX : event.movementY;

        const marginKey = this.vertical ? 'width' : 'height';
        let spacing = this.margins[marginKey] + delta;

        if (spacing < 0) {
          spacing = 0;
        }

        const previousKey = this.vertical ? 'right' : 'bottom';
        const nextKey = this.vertical ? 'left' : 'top';

        if (this.elements[this.rawPrevIndex][this.device].margin?.allSides) {
          this.elements[this.rawPrevIndex][this.device].margin.allSides = false;
        }
        this.elements[this.rawPrevIndex][this.device].margin[previousKey] = spacing;

        if (this.elements[this.rawNextIndex][this.device].margin?.allSides) {
          this.elements[this.rawNextIndex][this.device].margin.allSides = false;
        }
        this.elements[this.rawNextIndex][this.device].margin[nextKey] = spacing;
        this.regenerateElementStyles();

        this.debounceTrack({
          component1: this.elements[this.rawPrevIndex].type,
          component2: this.elements[this.rawNextIndex].type,
          spacing,
        });

        this.setMarginVariables();
      },
      leave() {
        this.$emit('interact', false);
        this.mousedown = false;
        this.$el.removeEventListener('mousemove', this.move);
      },
      isElementHidden(element) {
        return get(element, `${this.device}.hidden`, false);
      },
      mouseOver() {
        this.$emit('hover', this.$el);
      },
      mouseLeave() {
        this.$emit('leave', this.$el);
      },
    },
  };
</script>

<style lang="sass">
  @import "@/sass/variables/_colors.sass"

  .om-margin-visualizer
    position: absolute
    bottom: calc(0px - var(--space-em-height))
    background: transparent
    transition: opacity .1s
    display: flex
    justify-content: center
    align-items: center
    height: var(--space-em-height)
    min-height: 2px
    left: 0
    right: 0
    margin-left: auto
    margin-right: auto
    width: 100%
    z-index: 40
    &.vertical
      width: var(--space-em-width)
      height: 100%
      bottom: 0
      left: 0
      right: calc(0px - var(--space-em-width))
    &.inactive
      opacity: 0 !important
    &-handle
      margin-bottom: -1px
      opacity: 0
      height: 0
      background: $om-orange
      width: 16px
      pointer-events: none
      border: 2px solid $om-orange
      box-sizing: content-box !important
    &.scaled-mobile-preview
      .om-margin-visualizer-handle
        width: 20px
    &:hover
      .om-margin-visualizer-handle
        opacity: 1
    &-trigger
      height: 22px
      width: calc(16px + 20px)
      position: absolute
      left: calc(50% - 8px - 10px)
      top: calc(50% - 11px)
      z-index: 22
      cursor: row-resize
      opacity: 0
      &.vertical
        cursor: col-resize
      &:hover ~ .om-margin-visualizer-handle,
      &.mousedown ~ .om-margin-visualizer-handle
        opacity: 1
</style>
