.image-manager
  z-index: 99999991
  display: flex
  justify-content: center
  align-items: center
  .content
    display: flex
    flex-direction: column
    width: $image-manager-width
    height: $image-manager-width
  .main
    position: relative
    display: flex
    flex-direction: column
    overflow: auto
    padding: 1rem !important
    height: calc(100% - 4.4rem)
    &.no-img
      justify-content: center
      .upload-container
        flex-direction: column
      .upload-button
        margin-bottom: 1rem
      .upload-size
        margin-bottom: 3rem
      .images
        display: none
      .upload-area
        position: relative
        height: 100%
        width: 100%
  .upload-area
    display: flex
    flex-direction: row
    justify-content: center
    margin: 1rem 0
    transition: .3s cubic-bezier(.25,.8,.5,1)
    .upload
      &-container
        display: flex
        align-items: center
        justify-content: center
        .upload-button
          background-color: $om-orange
          margin-right: 1rem
          margin-left: 1rem
      &-size
        font-size: 0.8125rem
        color: #9ea2a6
        display: inline-block
        padding: .75rem 0
      &-width
        padding-right: .25rem
    .image-replace-toggle
      position: absolute
      right: 2.25rem
      .switch-label
        min-width: fit-content
        &-container
          margin-bottom: 0
          margin-right: .25rem
  .images
    display: flex
    flex-wrap: wrap
    width: 100%
    height: 100%
    justify-content: flex-start
    align-content: flex-start
    overflow: auto
    .progressive-image
      width: 100%
      height: 100%
    .image
      position: relative
      display: flex
      margin: 10px
      overflow: hidden
      flex-grow: 1
      max-width: 20rem
      box-shadow: 2px 4px 10px rgba(#000, 0.2)
      height: 13rem
      width: 13rem
      background: #ebebeb
      border: 1px solid #9e9e9e
      border-radius: 3px
      .image-hover
        display: flex
        position: absolute
        height: 100%
        width: 100%
        opacity: 0
        transition: opacity .5s ease-in-out
        flex-direction: column
        justify-content: space-evenly
        background: rgba(black, .7)
        z-index: 2
        > .button
          width: 90%
          align-self: center
          &:hover
            background: #ffffff
            color: #000000
            opacity: 1
        .button-group
          display: flex
          flex-direction: row
          justify-content: space-evenly
      &:hover
        .image-hover
          opacity: 1
      img,
      svg
        box-sizing: border-box
        object-fit: contain
        cursor: pointer
        width: 100%
        height: 100%
      .image-data
        width: 100%
        background: transparent
        color: #fff
        padding: 0 1rem
  .upload-drop-message
    display: flex
    background: rgba($om-orange-light, .4)
    border: 2px dashed $om-orange
    border-radius: $border-radius
    color: #ffffff
    width: calc(100% - 2rem)
    height: calc(100% - 2rem)
    align-items: center
    justify-content: center
    position: absolute
  .scrollable::-webkit-scrollbar
    width: 10px

/* scrollbar x-style */
.image-manager .scrollable::-webkit-scrollbar
  height: 6px
  background-color: #F5F5F5

.image-manager .scrollable::-webkit-scrollbar-thumb
  background-color: var(--brand-primary-color)
  border-radius: 10px

.image-manager .scrollable::-webkit-scrollbar-track
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3)
  border-radius: 10px
  background-color: #F5F5F5
/* /scrollbar x-style */

.upload
  &-cloud
    &-wrapper
      display: none
      &.no-img
        display: inline-block
        i
          color: #cccccc
          font-size: 10rem
      &.center-img
        display: inline-block
        position: absolute
        left: 50%
        top: 50%
        transform: translate(-50%, -50%)
        i
          color: lighten(#cccccc, 15%)
          font-size: 20rem

.image-name
  padding: .25rem
  background: #f7f8f9
  text-align: center
  color: #696d72
  border-radius: 3px
  font-weight: 600
  word-break: break-all
.image-res
  font-size: 0.6875rem
  padding: .25rem 0
.image-control-button
  background: #d2d5d7
  width: 6rem
  height: 6rem
  margin: .5rem

.image-manipulator.om-modal
  .content
    width: 85%
  .main
    height: calc(100% - 3.75rem)
