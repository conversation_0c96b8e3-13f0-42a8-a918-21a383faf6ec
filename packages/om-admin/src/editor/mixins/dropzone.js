import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';
import { get as _get } from 'lodash-es';
import { MockLayouts } from '@/editor/components/MockSite.vue';
import readOnlyElements from '@/editor/config/readOnlyElements';
import { inputElements } from '@om/editor-ssr-shared/src/utils';
import { hasElWheel } from '@/editor/util';
import { dragAndDropTracker } from '@/services/userInteractionTracker/tracker';
import { ELEMENTS } from '@/../../../libraries/template-properties/src/propertyHelper';

const DROPZONE_BOTTOM_PLACEMENT = {
  IN_CASE_OF_ONE: { 0: { bottom: true } },
  IN_CASE_OF_TWO: { 0: { bottom: false }, 1: { bottom: true } },
  IN_CASE_OF_MORE: {
    0: { bottom: false },
    MIDDLE: { bottom: false },
    LAST: { bottom: true },
  },
};

const TYPE_ELEMENT_STRUCTURE = ['OmCol', 'OmRow', 'OmPage'];

const isElementWithHoverbar = (type) =>
  type && !TYPE_ELEMENT_STRUCTURE.some((structureType) => structureType === type);

export default {
  props: {
    pageObject: {
      type: Object,
    },
    pageIndex: {
      type: Number,
    },
    pageHovered: {
      type: Boolean,
    },
    dragging: {
      type: Boolean,
    },
  },

  data() {
    return {
      hideRowId: null,
      mouseOverRowId: null,
      mouseOverColumnId: null,
      mouseOverElementId: null,
      enableDrag: false,
      dragStarted: false,
      isSelected: false,
      MockLayouts,
      disableRowDrag: null,
      isMarginActive: null,
      isPaddingActive: null,
    };
  },

  computed: {
    ...mapState([
      'editMode',
      'selectedPage',
      'teaserPreview',
      'dragInfo',
      'selectedElement',
      'template',
      'maxPageHeight',
      'selectedColumn',
      'templateSaveData',
      'mobilePreview',
      'docker',
    ]),
    ...mapGetters([
      'isEmbedded',
      'rowsOfPage',
      'hasWheel',
      'isNano',
      'columnsOfRow',
      'elementsOfColumn',
      'mockSiteLayout',
      'inEditor',
      'floatingImages',
      'hasAccountFeature',
      'isTemplateEditorMode',
    ]),
    closeDelay() {
      return this.template.style.closeButton.delay;
    },
    hasRow() {
      const row = this.template.elements.filter(
        (ele) => ele.type === 'OmRow' && ele.pageId === this.selectedPage.uid,
      );
      return !!row.length;
    },
    isVisualizerActive() {
      return this.isMarginActive || this.isPaddingActive;
    },
  },
  methods: {
    ...mapMutations(['setDragInfo', 'moveRow', 'setHoverbarActive']),
    ...mapActions([
      'removeRow',
      'copyElement',
      'removeElement',
      'addElement',
      'chooseInputField',
      'addRow',
    ]),
    hasElWheel,
    getElementDataTrackProperty(element) {
      return `component:${element.type}|change:x.elements.text.${element.type}`;
    },
    getHoverBarTitle(element) {
      if (this.docker) {
        return '';
      }
      const type = element?.type;
      const justEdit = ['OmLuckyWheel', 'OmScratchCard', 'OmPickAPresent'];

      if (justEdit.includes(type)) {
        return this.$t('hoverBar.justEdit');
      }

      return this.$t('hoverBar.clickToEditDragToMove');
    },
    needToGenerateDropzone(elementsInColumn) {
      if (elementsInColumn === 1) {
        return DROPZONE_BOTTOM_PLACEMENT.IN_CASE_OF_ONE;
      }

      if (elementsInColumn === 2) {
        return DROPZONE_BOTTOM_PLACEMENT.IN_CASE_OF_TWO;
      }

      return DROPZONE_BOTTOM_PLACEMENT.IN_CASE_OF_MORE;
    },
    needToGenerateBottom(columnId, elementIndex) {
      return this.needToGenerateDropzone(this.elementsOfColumn(columnId).length)[elementIndex]
        .bottom;
    },
    mouseEnterElementOnCanvas(element) {
      this.$bus?.$emit?.('mouseEnterElementOnCanvas', { uid: element.uid });

      if (element?.type === 'OmCol') {
        this.mouseOverColumnId = element.uid;
      }

      this.setHoverbarState(element?.type, true);
      this.mouseOverElementId = element.uid;
    },
    mouseLeaveElementOnCanvas(element) {
      this.$bus?.$emit?.('mouseLeaveElementOnCanvas', { uid: element.uid });

      if (element?.type === 'OmCol') {
        this.mouseOverColumnId = null;
      }

      this.setHoverbarState(element?.type, false);
      this.mouseOverElementId = null;
    },
    setHoverbarState(type, value) {
      if (isElementWithHoverbar(type)) this.setHoverbarActive(value);
    },
    isRowSelected(row) {
      if (this.selectedElement) {
        return (
          row.uid === this.selectedElement.uid ||
          (this.selectedElement.type === 'OmCol' && this.selectedElement.rowId === row.uid)
        );
      }
      return false;
    },
    isTheLastElement(index, lengthOfElement) {
      return index === lengthOfElement - 1;
    },
    isRowDraggable() {
      if (this.disableRowDrag) return false;
      if (this.selectedElement !== null) {
        const { type } = this.selectedElement;

        return this.editMode && !['OmButton', 'OmText'].includes(type);
      }
      return this.editMode;
    },
    dragstart(e, type, element, col = null, row = null) {
      // disable drag for button and text is selected
      const { uid } = element;
      if (type === 'element') {
        const type = element.data.type;
        if (type === 'button' && this.selectedElement && this.selectedElement.uid === element.uid) {
          e.preventDefault();
          return;
        }
      }

      e.stopPropagation(); // only trigger 1 dragstart on element drag

      const tracker = dragAndDropTracker.create();
      tracker.start(element.type, e.target);
      tracker.setDrag();

      this.setDragInfo({
        type,
        data: {
          source: uid,
          move: true,
          sourceElement: element,
          sourceColumn: col,
          sourceRow: row,
        },
        tracker,
      });

      this.handleBeforeDragstart(e);
    },
    dragend(e) {
      this.$emit('update:dragging', false);
      this.dragInfo.tracker.end();
      this.handleAfterDragend(e);
    },
    getOmElement(event) {
      if (event.target.classList.contains('.om-divider-helper')) {
        return event.target.nextSibling;
      }

      return event.target.closest('.om-element') || event.target;
    },
    getHoverBarElement(element) {
      return (
        element.querySelector('.hoverbar-dashed') ||
        element.querySelector('.element-hoverbar-dashed')
      );
    },
    handleBeforeDragstart(e) {
      this.dragStarted = true;
      const omElement = this.getOmElement(e);
      const isRow = omElement.id.includes('row');
      omElement.classList.add('drag-started');
      const hoverBar = this.getHoverBarElement(omElement);

      if (isRow) {
        omElement.parentElement.classList.add('selected');
        return;
      }

      if (hoverBar.classList.contains('selected')) {
        this.isSelected = true;
        hoverBar.classList.remove('selected');
      }
    },
    handleAfterDragend(e) {
      this.dragStarted = false;
      const omElement = this.getOmElement(e);
      const isRow = omElement.id.includes('row');
      const hoverBar = this.getHoverBarElement(omElement);
      hoverBar.classList.remove('on-drag');
      omElement.classList.remove('drag-started');

      if (isRow) {
        omElement.parentElement.classList.add('selected');
        return;
      }

      if (this.isSelected) {
        this.isSelected = false;
        hoverBar.classList.add('selected');
      }
    },
    mouseOverRow(row) {
      this.hideRowId = null;
      this.mouseOverRowId = row.uid;
    },
    mouseLeaveRow() {
      this.hideRowId = null;
      this.mouseOverRowId = null;
    },
    async drop(e, dropLocation) {
      this.$emit('update:dragging', false);
      this.dragInfo.tracker.setDrop();

      let target;
      if (dropLocation.element) {
        target = dropLocation.element;
      } else if (dropLocation.column) {
        target = dropLocation.column;
      } else if (dropLocation.row) {
        target = dropLocation.row;
      } else if (dropLocation.page) {
        target = dropLocation.page;
      }
      const uid = target.uid;

      this.$store.state.dropLocation = {
        uid,
        isTop: dropLocation.position === 'top',
        position: dropLocation.position,
      };

      if (this.dragInfo.type === 'element') {
        if (this.dragInfo.data && this.dragInfo.data.source) {
          // An existing element was moved
          this.addElement();
        } else {
          // Add new element
          const type = this.dragInfo.data;

          if (type === 'Inputs') {
            window.parent.om.bus.$emit('changeFormManagerVisibility', { show: 'firstStep' });
            return;
          }

          let customField;
          const TYPE = `Om${type}`;
          if (inputElements.includes(TYPE)) {
            customField = await this.chooseInputField({ inputType: type.toLowerCase() });
          }

          if (
            !this.isTemplateEditorMode &&
            [ELEMENTS.OmSurvey, ELEMENTS.OmFeedback].includes(TYPE)
          ) {
            this.addElement();
            window.parent.om.bus.$emit('changeFormManagerVisibility', {
              show: 'missingCustomFields',
              meta: { type: TYPE },
            });
          } else {
            this.addElement({ customField });
          }
        }
      } else if (this.dragInfo.type === 'row') {
        if (this.dragInfo.data && this.dragInfo.data.source) {
          // An existing row was moved
          this.moveRow({
            source: this.dragInfo.data.source,
            before: dropLocation.position === 'top',
            target: dropLocation.row.uid,
          });
        } else {
          // A new row should be added
          this.addRow({
            uid: target.type === 'OmRow' ? uid : null,
            pageId: dropLocation.page.uid,
            before: dropLocation.position === 'top',
            count: this.dragInfo.data,
          });
        }
      }
    },
    contentAlignment(column) {
      if (this.isNano) {
        return `om-justify-${column.desktop.contentAlignment}`;
      }
      return '';
    },
    columnSelected(column) {
      if (!this.selectedColumn) return '';
      return column.uid === this.selectedColumn.uid ? 'selected' : '';
    },
    extraElementClass(element) {
      const isHiddenDesktop = _get(element, 'desktop.hidden', false);
      const isHiddenMobile = _get(element, 'mobile.hidden', false);
      const formType = _get(element, 'data.form.customSettings.type', false);
      const dropDown = formType === 'phoneNumber';
      const customClasses = _get(element, 'data.customClasses', '');

      return {
        'xs-up-hidden': isHiddenDesktop,
        'xs-hidden': isHiddenMobile,
        'om-dropdown': dropDown,
        [customClasses]: !!customClasses,
      };
    },
    isElementDraggable(element) {
      const notDraggableActive =
        ['OmButton', 'OmText'].includes(element.type) &&
        this.selectedElement &&
        element.uid === this.selectedElement.uid;
      const result = this.editMode && !notDraggableActive && !this.readOnlyElement(element);
      return result;
    },
    hoverbarMousedown(element) {
      if (this.hoverBarClasses(element).bottom) {
        setTimeout(() => {
          document.querySelector('.ql-toolbar').style.marginTop = '20px';
        }, 100);
      }
      this.enableDrag = true;
    },
    hoverbarMouseup() {
      this.enableDrag = false;
    },
    mouseOverElement(row) {
      this.$bus?.$emit?.('elementHovered', true);
      this.hideRowId = row.uid;
      this.mouseOverRowId = null;
      this.mouseOverColumnId = null;
      this.dragStarted = false;
    },
    mouseLeaveElement() {
      this.$bus?.$emit?.('elementHovered', false);
      this.hideRowId = null;
      this.mouseOverRowId = null;
    },
    drag(event) {
      this.$emit('update:dragging', true);
      const omElement = this.getOmElement(event);
      const hoverBar = this.getHoverBarElement(omElement);

      if (!hoverBar || hoverBar.classList.contains('on-drag')) return;

      hoverBar.classList.add('on-drag');
    },
    readOnlyElement(element) {
      if (this.templateSaveData) {
        return this.templateSaveData.type === 'variant' && readOnlyElements.includes(element.type);
      }
      return false;
    },
    navigateTo(pane) {
      this.$bus?.$emit?.('activatePane', { pane, level: 2 });
    },
    isFeedbackOrSurvey(element) {
      return element.type === 'OmFeedback' || element.type === 'OmSurvey';
    },
    hoverBarClasses(element) {
      const classes = {};

      if (!this.isNano) {
        classes.top = true;
        return classes;
      }

      const el = document.querySelector(`#${element.uid}`);
      if (!el) return classes;

      const rect = el.getBoundingClientRect();

      if (rect.top < 26) {
        classes.bottom = true;
      } else {
        classes.top = true;
      }

      if (rect.width < 200) {
        classes.stacked = true;
      }

      return classes;
    },
    toggleMarginActive(active) {
      this.isMarginActive = active;
    },
    togglePaddingActive(active) {
      this.isPaddingActive = active;
    },
  },
};
