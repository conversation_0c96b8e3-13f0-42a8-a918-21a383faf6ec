import CREATE_CAMPAIGN from '@/graphql/CreateCampaign.gql';
import { apolloClient } from '@/apollo';
import GENERATE_SITE_EXTRACTIONS from '@/graphql/GenerateSiteExtractions.gql';
import GENERATE_AUTO_THEME from '@/graphql/GenerateAutoTheme.gql';
import AVAILABLE_TEMPLATE_LANGUAGES from '@/graphql/AvailableTemplateLanguages.gql';
import Vue from 'vue';

const FEATURE_FLAG = 'MINI_WIZARD';

const STEPS = [
  'template-preview',
  'name-campaign',
  'new-campaign-sms-prompt',
  'mini-wizard-auto-personalize',
  'mini-wizard-fine-tune',
];

export default {
  namespaced: true,
  state: {
    generating: {},
    step: null,
    siteDataByDomains: {},
    templateId: null,
    colors: [],
    theme: null,
    themeKit: null,
    needsSMSPrompt: false,
    isDynamicContent: false,
    addControlVariant: false,
    domain: null,
    source: null,
    sourceUrl: null,
    template: null,
    skipMiniWizard: false,
    autoPersonalization: {
      color: null,
      rounding: null,
      mainFont: null,
      secondaryFont: null,
      logo: null,
      language: 'en',
    },
    hasEnoughData: true,
    removeSMSPage: false,
    availableLanguages: [],
  },
  mutations: {
    setThemeKit(state, themeKit) {
      state.themeKit = themeKit;
    },
    setDomain(state, domain) {
      state.domain = domain;
    },
    setSource(state, source) {
      state.source = source;
    },
    setSourceUrl(state, sourceUrl) {
      state.sourceUrl = sourceUrl;
    },
    setCurrentStep(state, step) {
      state.step = step;
    },
    setTemplate(state, template) {
      const templateObj = JSON.parse(template.template);

      state.template = {
        ...template,
        template: templateObj,
      };
    },
    setThemeId(state, id) {
      state.theme = id;
    },
    setGenerating(state, { domainId, isGenerating }) {
      Vue.set(state.generating, domainId, isGenerating);
    },
    setAutoPersonalizationProperty(state, { key, value }) {
      Vue.set(state.autoPersonalization, key, value);
    },
    resetAutoPersonalization(state) {
      state.autoPersonalization = {
        color: null,
        rounding: null,
        mainFont: null,
        secondaryFont: null,
        language: 'en',
      };
      state.hasEnoughData = true;
    },
    setSkipMiniWizard(state, skip) {
      state.skipMiniWizard = skip;
    },
    setHasEnoughData(state, hasEnough) {
      state.hasEnoughData = hasEnough;
    },
    setRemoveSMSPage(state, removeSMSPage) {
      state.removeSMSPage = removeSMSPage;
    },
    setThemeColors(state, themeColors) {
      Vue.set(state.themeKit.colors, 'themeColors', themeColors);
    },
    setSiteDataByDomains(state, siteData) {
      state.siteDataByDomains = siteData;
    },
    setSiteDataForDomain(state, { domainId, language, logo, siteTheme }) {
      Vue.set(state.siteDataByDomains, domainId, { language, logo, siteTheme });
    },
    setAvailableLanguages(state, languages) {
      state.availableLanguages = languages;
    },
  },
  actions: {
    async initCampaignCreation({ state, dispatch }, params) {
      state.templateId = params.templateId || null;
      state.colors = params.colors || [];
      state.theme = params.theme || undefined;
      state.needsSMSPrompt = params.needsSMSPrompt ?? false;
      state.isDynamicContent = params.isDynamicContent ?? false;
      state.addControlVariant = params.addControlVariant ?? false;

      state.domainId = null;
      state.source = null;

      if (!state.availableLanguages?.length) {
        dispatch('getAvailableTemplateLanguages');
      }
    },
    async getAvailableTemplateLanguages({ commit }) {
      const { data } = await apolloClient.query({
        query: AVAILABLE_TEMPLATE_LANGUAGES,
      });

      commit('setAvailableLanguages', Object.keys(data.availableTemplateLanguages));
    },
    createCampaign({ state }, extraParams) {
      return apolloClient.mutate({
        mutation: CREATE_CAMPAIGN,
        variables: {
          input: {
            templateId: state.templateId,
            colors: state.colors,
            theme: state.theme,
            dynamicContent: state.isDynamicContent,
            addControlVariant: state.addControlVariant,
            domainId: state.domain._id,
            source: state.source,
            removeSMSPage: state.removeSMSPage,
            ...extraParams,
          },
        },
      });
    },
    async generateSiteData({ state, commit, dispatch }, domainId) {
      if (state.siteDataByDomains[domainId]) return;

      commit('setGenerating', { domainId, isGenerating: true });

      try {
        // eslint-disable-next-line no-unused-vars
        const [autoTheme, siteExtractions] = await Promise.all([
          apolloClient.query({
            query: GENERATE_AUTO_THEME,
            variables: {
              domainId,
            },
          }),
          apolloClient.query({
            query: GENERATE_SITE_EXTRACTIONS,
            variables: {
              domainId,
            },
          }),
        ]);

        const language = siteExtractions?.data?.generateSiteExtractions?.language;
        const logo = siteExtractions?.data?.generateSiteExtractions?.logo;
        const siteTheme = autoTheme?.data?.generateAutoTheme;

        commit('setSiteDataForDomain', { domainId, language, logo, siteTheme });
      } catch (_) {
        commit('setGenerating', { domainId, isGenerating: false });
      }

      commit('setGenerating', { domainId, isGenerating: false });
    },
  },
  getters: {
    previousCampaignCreationStep(state) {
      const currentStepIndex = STEPS.findIndex((step) => step === state.step);
      if (currentStepIndex > 0) {
        if (state.step === 'mini-wizard-auto-personalize') {
          return state.needsSMSPrompt ? 'new-campaign-sms-prompt' : 'name-campaign';
        }

        if (state.step === 'mini-wizard-fine-tune') {
          return state.hasEnoughData
            ? 'mini-wizard-auto-personalize'
            : state.needsSMSPrompt
            ? 'new-campaign-sms-prompt'
            : 'name-campaign';
        }

        return STEPS[currentStepIndex - 1];
      }

      return null;
    },
    isGenerationLoadingForCurrentDomain(state) {
      return !!state.generating[state.domain?._id];
    },
    showMiniWizard(state, _, __, rootGetters) {
      if (state.skipMiniWizard) return false;

      const isBaseTheme =
        state.themeKit?.name &&
        (!state.themeKit?.source || state.themeKit?.name === state.themeKit?.source);
      const hasFlag = rootGetters.hasAccountFeature(FEATURE_FLAG);

      return isBaseTheme && hasFlag;
    },
  },
};
