import Vue from 'vue';
import { get as _get, setWith } from 'lodash-es';
import { calcImageInitialWidth, getSelectedImageHeight } from '@/editor/util';
import ALL_IMAGES from '@/graphql/AllImages.gql';
import ALL_TEMPLATE_IMAGES from '@/graphql/AllTemplateImages.gql';
import { apolloClient } from '@/apollo';

const _set = (target, key, value) =>
  setWith(target, key, value, (ns, key, target) => {
    Vue.set(target, key, ns);
  });

const handleColorInstance = ({ commit, colorInstance, dispatch, _id, url }) => {
  colorInstance.setImage(_id, url);
  colorInstance.setImageCrop(null);
  if (colorInstance.getImagePosition() === 'crop') {
    colorInstance.setImagePosition('cover');
  }
  commit('handleColorInstanceToStore', colorInstance, { root: true });

  if (
    ['OmPage', 'OmRow', 'OmCol'].includes(colorInstance.element.type) &&
    colorInstance.baseProperty.includes('background')
  ) {
    dispatch(
      'analytics/reportEditorEvent',
      {
        interactionType: 'settingChanged',
        location: 'rightPane',
        extras: {
          elementType: colorInstance.element.type,
          eventType: 'backgroundImageChange',
          property: colorInstance.baseProperty,
          settingName: colorInstance.baseProperty,
          settingValue: colorInstance.image.url,
        },
        logrocketEventName: 'backgroundImageChange',
      },
      { root: true },
    );
  }
};

const handleImageChange = ({
  rootState,
  commit,
  dispatch,
  targetAttr,
  saveImageUrl,
  editedDevice,
  selectedPageEl,
  _id,
  url,
}) => {
  if (targetAttr === 'selectedElement') {
    const element = rootState.selectedElement;
    const device = editedDevice || rootState.mobilePreview ? 'mobile' : 'desktop';

    const propertyPrefix = `${targetAttr}.${device}`;

    dispatch('handleMissingDesktopProperties', { saveImageUrl, url, _id });
    _set(rootState, `${propertyPrefix}.background.imageId`, _id);

    if (url) {
      _set(rootState, `${propertyPrefix}.background.imageUrl`, url);
    }

    if (['OmImage', 'OmFloatingImage'].includes(element.type)) {
      const width = calcImageInitialWidth(element);
      const height = getSelectedImageHeight();

      _set(rootState, `${propertyPrefix}.smartSize.width`, width);
      _set(rootState, `${propertyPrefix}.smartSize.type`, 'manual');

      _set(rootState, `${targetAttr}.desktop.background.color`, null);
      _set(rootState, `${targetAttr}.mobile.background.color`, null);
      _set(rootState, `${targetAttr}.desktop.background.type`, 'transparent');
      _set(rootState, `${targetAttr}.mobile.background.type`, 'transparent');

      if (element.type === 'OmFloatingImage' && device !== 'mobile' && selectedPageEl) {
        let x = _get(rootState, `${propertyPrefix}.position.left`);
        let y = _get(rootState, `${propertyPrefix}.position.top`);

        x -= width / 2;
        y -= height / 2;

        const percentageX = (x / selectedPageEl.offsetWidth) * 100;
        const percentageY = (y / selectedPageEl.offsetHeight) * 100;

        _set(rootState, `${propertyPrefix}.position.left`, percentageX);
        _set(rootState, `${propertyPrefix}.position.top`, percentageY);
      }
    }

    window.om.store._vm.$bus.$emit('regenerateElementStyles', { type: element.type });
  } else {
    commit('updateData', { property: `${targetAttr}.imageId`, value: _id }, { root: true });
    commit(
      'updateData',
      {
        property: `${targetAttr}.imageUrl`,
        value: url,
      },
      { root: true },
    );
  }
};

export default {
  namespaced: true,
  state: {
    images: [],
    allTemplateImages: [],
    showImageManager: false,
    imageManagerDestination: {},
    imageManagerLogoUpload: false,
  },
  getters: {},

  mutations: {
    showImageManager(state) {
      state.showImageManager = true;
    },
    hideImageManager(state) {
      state.showImageManager = false;
    },
    setImageManagerDestination(state, value) {
      state.imageManagerDestination = value;
    },
    setImageManagerLogoUpload(state, value) {
      state.imageManagerLogoUpload = value;
    },
    setImages(state, value) {
      state.images = value;
    },
    setTemplateImages(state, value) {
      state.allTemplateImages = value;
    },
  },
  actions: {
    async fetchImages({ commit }, variables = {}) {
      const {
        data: { images },
      } = await apolloClient.query({
        query: ALL_IMAGES,
        variables,
      });

      commit('setImages', images);

      return images;
    },
    async fetchTemplateImages({ commit }, variables = {}) {
      const {
        data: { allTemplateImages },
      } = await apolloClient.query({
        query: ALL_TEMPLATE_IMAGES,
        variables,
      });

      commit('setTemplateImages', allTemplateImages);

      return allTemplateImages;
    },
    replaceImage({ commit, dispatch, state, rootState }, { image, selectedPageEl }) {
      const { targetAttr, saveImageUrl, editedDevice, colorInstance } =
        state.imageManagerDestination;
      const { _id, url } = image;

      if (colorInstance) {
        handleColorInstance({ commit, colorInstance, dispatch, _id, url });
        return;
      }

      handleImageChange({
        rootState,
        commit,
        dispatch,
        targetAttr,
        saveImageUrl,
        editedDevice,
        selectedPageEl,
        _id,
        url,
      });
    },
    hideImageManager({ rootState, commit }) {
      if (
        (rootState.selectedElement === 'OmImage' &&
          !rootState.selectedElement.desktop.background.imageId) ||
        (['OmProduct', 'OmScratchCard', 'OmPickAPresent'].includes(rootState.selectedElement) &&
          !rootState.selectedElement.data.image.imageId)
      ) {
        window.om.bus.$emit('removeElement', { uid: rootState.selectedElement.uid });
      }
      commit('hideImageManager');
    },
    handleMissingDesktopProperties({ rootState }, { saveImageUrl, url, _id }) {
      // We need to set desktop image also when the user set the image first on mobile
      const hasDesktopValue = !!_get(rootState, 'selectedElement.desktop.background.imageId');
      if (rootState.mobilePreview && hasDesktopValue) return;

      _set(rootState, `selectedElement.desktop.background.imageId`, _id);
      if (saveImageUrl || url) {
        _set(rootState, `selectedElement.desktop.background.imageUrl`, url);
      }
    },
  },
};
